{"Version": 1, "WorkspaceRootPath": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.users.sql||{0058A1F7-65F3-4DB9-B3D0-CA7E64DD73CD}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\tasktypescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\tasktypescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\reportschedulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\reportschedulescontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\dashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\dashboardcontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|e:\\flutter_application_2_converttosql - copy2\\webapi\\webapi\\controllers\\dashboardscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{3CBA8765-032D-413E-9DBE-B19C21A9B860}|webApi\\webApi.csproj|solutionrelative:webapi\\controllers\\dashboardscontroller.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 1, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedHeight": 398, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 1, "Title": "TaskTypesController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\TaskTypesController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\TaskTypesController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\TaskTypesController.cs", "RelativeToolTip": "webApi\\Controllers\\TaskTypesController.cs", "ViewState": "AgIAAKgAAAAAAAAAAAAAAAwBAAANAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-27T14:12:41.44Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "ReportSchedulesController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\ReportSchedulesController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\ReportSchedulesController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\ReportSchedulesController.cs", "RelativeToolTip": "webApi\\Controllers\\ReportSchedulesController.cs", "ViewState": "AgIAAEgBAAAAAAAAAAAgwFsBAABPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-27T14:00:41.789Z"}, {"$type": "Document", "DocumentIndex": 0, "Title": "dbo.users.sql", "DocumentMoniker": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.users.sql", "ToolTip": "ViewDataWindow_MSSQL__/._sqlexpress/databasetasks/True/SqlTable/dbo.users.sql", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000826|", "WhenOpened": "2025-07-26T14:01:28.367Z"}, {"$type": "Document", "DocumentIndex": 3, "Title": "DashboardController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\DashboardController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\DashboardController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\DashboardController.cs", "RelativeToolTip": "webApi\\Controllers\\DashboardController.cs", "ViewState": "AgIAACEBAAAAAAAAAAAgwEgAAAAmAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T13:45:00.924Z"}, {"$type": "Document", "DocumentIndex": 4, "Title": "DashboardsController.cs", "DocumentMoniker": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\DashboardsController.cs", "RelativeDocumentMoniker": "webApi\\Controllers\\DashboardsController.cs", "ToolTip": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\Controllers\\DashboardsController.cs", "RelativeToolTip": "webApi\\Controllers\\DashboardsController.cs", "ViewState": "AgIAADkAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-26T13:44:56.633Z"}]}]}]}