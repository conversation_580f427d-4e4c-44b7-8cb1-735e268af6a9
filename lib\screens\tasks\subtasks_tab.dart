import 'package:flutter/material.dart';
import 'package:flutter_application_2/constants/app_colors.dart';
import 'package:flutter_application_2/constants/app_styles.dart';
import 'package:flutter_application_2/models/subtask_models.dart';
import 'package:flutter_application_2/screens/widgets/common/loading_indicator.dart';
import 'package:flutter_application_2/screens/widgets/common/empty_state_widget.dart';
import 'package:get/get.dart';
import '../../controllers/subtasks_controller.dart';
import '../../models/task_model.dart';
import '../../services/unified_permission_service.dart';
import '../../services/unified_signalr_service.dart';


/// علامة تبويب المهام الفرعية مع دعم التحديثات الفورية
/// تعرض قائمة المهام الفرعية للمهمة الحالية وتتيح إضافة وتعديل وحذف المهام الفرعية
/// مع تحديثات فورية عبر SignalR
class SubtasksTab extends StatefulWidget {
  final Task task;

  const SubtasksTab({super.key, required this.task});

  @override
  State<SubtasksTab> createState() => _SubtasksTabState();
}

class _SubtasksTabState extends State<SubtasksTab> {
  final TextEditingController _titleController = TextEditingController();
  late SubtasksController controller;
  late UnifiedSignalRService _signalRService;
  late UnifiedPermissionService _permissionService;

  @override
  void initState() {
    super.initState();

    // تهيئة المتحكمات والخدمات
    controller = Get.find<SubtasksController>();
    _signalRService = Get.find<UnifiedSignalRService>();
    _permissionService = Get.find<UnifiedPermissionService>();

    // إعداد مستمعات SignalR للتحديثات الفورية
    _setupSignalRListeners();

    // الانضمام لمجموعة المهمة للحصول على تحديثات المهام الفرعية
    _joinTaskGroup();
  }

  @override
  void dispose() {
    // مغادرة مجموعة المهمة عند إغلاق التبويب
    _leaveTaskGroup();
    _titleController.dispose();
    super.dispose();
  }

  /// إعداد مستمعات SignalR للتحديثات الفورية
  void _setupSignalRListeners() {
    try {
      // الاستماع لإضافة مهمة فرعية جديدة
      _signalRService.taskHubConnection?.on("SubtaskAdded", (arguments) async {
        debugPrint("🔄 SignalR: تم استقبال إشعار إضافة مهمة فرعية: $arguments");
        await _handleSubtaskAdded(arguments);
      });

      // الاستماع لتحديث مهمة فرعية
      _signalRService.taskHubConnection?.on("SubtaskUpdated", (arguments) async {
        debugPrint("🔄 SignalR: تم استقبال إشعار تحديث مهمة فرعية: $arguments");
        await _handleSubtaskUpdated(arguments);
      });

      // الاستماع لحذف مهمة فرعية
      _signalRService.taskHubConnection?.on("SubtaskDeleted", (arguments) async {
        debugPrint("🔄 SignalR: تم استقبال إشعار حذف مهمة فرعية: $arguments");
        await _handleSubtaskDeleted(arguments);
      });

      debugPrint("✅ تم إعداد مستمعات SignalR للمهام الفرعية بنجاح");
    } catch (e) {
      debugPrint("❌ خطأ في إعداد مستمعات SignalR للمهام الفرعية: $e");
    }
  }

  /// الانضمام لمجموعة المهمة
  void _joinTaskGroup() {
    try {
      _signalRService.joinTaskGroup(widget.task.id.toString());
      debugPrint("✅ تم الانضمام لمجموعة المهمة ${widget.task.id} للمهام الفرعية");
    } catch (e) {
      debugPrint("❌ خطأ في الانضمام لمجموعة المهمة: $e");
    }
  }

  /// مغادرة مجموعة المهمة
  void _leaveTaskGroup() {
    try {
      _signalRService.leaveTaskGroup(widget.task.id.toString());
      debugPrint("✅ تم مغادرة مجموعة المهمة ${widget.task.id} للمهام الفرعية");
    } catch (e) {
      debugPrint("❌ خطأ في مغادرة مجموعة المهمة: $e");
    }
  }

  /// معالجة إضافة مهمة فرعية جديدة
  Future<void> _handleSubtaskAdded(List<dynamic>? arguments) async {
    try {
      if (arguments == null || arguments.isEmpty) return;

      final subtaskData = arguments[0];
      if (subtaskData is Map<String, dynamic>) {
        final taskId = subtaskData['taskId'] ?? subtaskData['TaskId'];

        // التحقق من أن المهمة الفرعية تخص المهمة الحالية
        if (taskId == widget.task.id) {
          // استخدام الطريقة المحسنة لإعادة التحميل
          await controller.refreshSubtasksByTask(widget.task.id);

          if (mounted) {
            setState(() {});
          }

          debugPrint("✅ تم تحديث قائمة المهام الفرعية بعد الإضافة عبر SignalR");
        }
      }
    } catch (e) {
      debugPrint("❌ خطأ في معالجة إضافة مهمة فرعية: $e");
    }
  }

  /// معالجة تحديث مهمة فرعية
  Future<void> _handleSubtaskUpdated(List<dynamic>? arguments) async {
    try {
      if (arguments == null || arguments.isEmpty) return;

      final subtaskData = arguments[0];
      if (subtaskData is Map<String, dynamic>) {
        final taskId = subtaskData['taskId'] ?? subtaskData['TaskId'];

        // التحقق من أن المهمة الفرعية تخص المهمة الحالية
        if (taskId == widget.task.id) {
          // استخدام الطريقة المحسنة لإعادة التحميل
          await controller.refreshSubtasksByTask(widget.task.id);

          if (mounted) {
            setState(() {});
          }

          debugPrint("✅ تم تحديث قائمة المهام الفرعية بعد التحديث عبر SignalR");
        }
      }
    } catch (e) {
      debugPrint("❌ خطأ في معالجة تحديث مهمة فرعية: $e");
    }
  }

  /// معالجة حذف مهمة فرعية
  Future<void> _handleSubtaskDeleted(List<dynamic>? arguments) async {
    try {
      if (arguments == null || arguments.isEmpty) return;

      final subtaskData = arguments[0];
      if (subtaskData is Map<String, dynamic>) {
        final taskId = subtaskData['taskId'] ?? subtaskData['TaskId'];

        // التحقق من أن المهمة الفرعية تخص المهمة الحالية
        if (taskId == widget.task.id) {
          // استخدام الطريقة المحسنة لإعادة التحميل
          await controller.refreshSubtasksByTask(widget.task.id);

          if (mounted) {
            setState(() {});
          }

          debugPrint("✅ تم تحديث قائمة المهام الفرعية بعد الحذف عبر SignalR");
        }
      }
    } catch (e) {
      debugPrint("❌ خطأ في معالجة حذف مهمة فرعية: $e");
    }
  }

  /// تحميل المهام الفرعية
  Future<void> _loadSubtasks() async {
    await controller.getSubtasksByTask(widget.task.id);
  }

  /// عرض مربع حوار إضافة مهمة فرعية
  void _showAddSubtaskDialog() {
    if (!_permissionService.canCreateTask()) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لإنشاء مهام فرعية',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(51),
        colorText: AppColors.error,
      );
      return;
    }

    _titleController.clear();
    Get.dialog(
      Dialog(
        child: Container(
          width: 400,
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                'إضافة مهمة فرعية',
                style: AppStyles.titleMedium.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w800,
                ),
              ),
              const SizedBox(height: 16),
              TextField(
                controller: _titleController,
                decoration: const InputDecoration(
                  labelText: 'عنوان المهمة الفرعية',
                  border: OutlineInputBorder(),
                ),
                autofocus: true,
              ),
              const SizedBox(height: 16),
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  TextButton(
                    onPressed: () => Get.back(),
                    child: const Text('إلغاء'),
                  ),
                  const SizedBox(width: 8),
                  ElevatedButton(
                    onPressed: () async {
                      if (_titleController.text.trim().isEmpty) {
                        Get.snackbar('خطأ', 'يرجى إدخال عنوان للمهمة الفرعية');
                        return;
                      }

                      final success = await controller.createSubtask(
                        widget.task.id,
                        _titleController.text.trim()
                      );

                      Get.back();

                      if (success) {
                        Get.snackbar(
                          'تم بنجاح',
                          'تم إضافة المهمة الفرعية بنجاح',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: AppColors.success.withAlpha(51),
                          colorText: AppColors.success,
                        );
                      }
                    },
                    child: const Text('إضافة'),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// تحديث حالة إكمال المهمة الفرعية مع تحديث فوري
  Future<void> _toggleSubtaskCompletion(Subtask subtask, bool isCompleted) async {
    try {
      bool success;
      if (isCompleted) {
        success = await controller.markAsCompleted(subtask.id);
      } else {
        success = await controller.markAsIncomplete(subtask.id);
      }

      if (success) {
        // لا نحتاج لإعادة التحميل يدوياً - SignalR سيتولى ذلك
        debugPrint("✅ تم تحديث حالة المهمة الفرعية - SignalR سيحدث الواجهة تلقائياً");

        // إظهار رسالة نجاح
        Get.snackbar(
          'تم بنجاح',
          isCompleted ? 'تم تحديد المهمة الفرعية كمكتملة' : 'تم تحديد المهمة الفرعية كغير مكتملة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success.withAlpha(51),
          colorText: AppColors.success,
          duration: const Duration(seconds: 2),
        );
      } else {
        Get.snackbar(
          'خطأ',
          'فشل في تحديث حالة المهمة الفرعية',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(51),
          colorText: AppColors.error,
        );
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'فشل في تحديث حالة المهمة الفرعية: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error.withAlpha(51),
        colorText: AppColors.error,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        // عنوان وزر إضافة
        Padding(
          padding: const EdgeInsets.all(16),
          child: LayoutBuilder(
            builder: (context, constraints) {
              // Check if we need to use a responsive layout
              final isSmallScreen = constraints.maxWidth < 400;

              if (isSmallScreen) {
                // Use a column layout for small screens
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            'المهام الفرعية',
                            style: AppStyles.titleMedium.copyWith(
                              color: AppColors.primary,
                              fontWeight: FontWeight.w800,
                              shadows: [
                                Shadow(
                                  color: AppColors.getShadowColor(0.2),
                                  blurRadius: 1,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        // مؤشر حالة الاتصال بـ SignalR
                        Obx(() => Container(
                          width: 8,
                          height: 8,
                          decoration: BoxDecoration(
                            color: _signalRService.isTaskHubConnected
                                ? AppColors.success
                                : AppColors.error,
                            shape: BoxShape.circle,
                          ),
                        )),
                        const SizedBox(width: 4),
                        Obx(() => Text(
                          _signalRService.isTaskHubConnected ? 'متصل' : 'غير متصل',
                          style: AppStyles.bodySmall.copyWith(
                            color: _signalRService.isTaskHubConnected
                                ? AppColors.success
                                : AppColors.error,
                            fontSize: 10,
                          ),
                        )),
                      ],
                    ),
                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: _showAddSubtaskDialog,
                        icon: const Icon(Icons.add),
                        label: Text('إضافة مهمة فرعية', style: TextStyle(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                        )),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: AppColors.primary,
                          foregroundColor: AppColors.white,
                        ),
                      ),
                    ),
                  ],
                );
              } else {
                // Use a row layout for larger screens
                return Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  textDirection: TextDirection.rtl,
                  children: [
                    Flexible(
                      child: Row(
                        children: [
                          Expanded(
                            child: Text(
                              'المهام الفرعية',
                              style: AppStyles.titleMedium.copyWith(
                                color: AppColors.primary,
                                fontWeight: FontWeight.w800,
                                shadows: [
                                  Shadow(
                                    color: AppColors.getShadowColor(0.2),
                                    blurRadius: 1,
                                    offset: const Offset(0, 1),
                                  ),
                                ],
                              ),
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          // مؤشر حالة الاتصال بـ SignalR
                          Obx(() => Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _signalRService.isTaskHubConnected
                                  ? AppColors.success
                                  : AppColors.error,
                              shape: BoxShape.circle,
                            ),
                          )),
                          const SizedBox(width: 4),
                          Obx(() => Text(
                            _signalRService.isTaskHubConnected ? 'متصل' : 'غير متصل',
                            style: AppStyles.bodySmall.copyWith(
                              color: _signalRService.isTaskHubConnected
                                  ? AppColors.success
                                  : AppColors.error,
                              fontSize: 10,
                            ),
                          )),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    ElevatedButton.icon(
                      onPressed: _showAddSubtaskDialog,
                      icon: const Icon(Icons.add,color: Colors.white),
                      label: Text('إضافة مهمة فرعية', style: TextStyle(
                        color: AppColors.white,
                        fontWeight: FontWeight.w600,
                      )),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: AppColors.white,
                      ),
                    ),
                  ],
                );
              }
            },
          ),
        ),

        // قائمة المهام الفرعية
        Expanded(
          child: Obx(() {
            // تحميل المهام الفرعية عند أول عرض
            if (controller.taskSubtasks.isEmpty && !controller.isLoading) {
              WidgetsBinding.instance.addPostFrameCallback((_) {
                _loadSubtasks();
              });
            }

            if (controller.isLoading) {
              return const Center(child: LoadingIndicator());
            }

            if (controller.taskSubtasks.isEmpty) {
              return const EmptyStateWidget(
                icon: Icons.task_alt,
                message: 'لا توجد مهام فرعية\nقم بإضافة مهام فرعية لتتبع التقدم بشكل أفضل',
              );
            }

            return ListView.builder(
              itemCount: controller.taskSubtasks.length,
              itemBuilder: (context, index) {
                final subtask = controller.taskSubtasks[index];
                return _buildSubtaskItem(subtask);
              },
            );
          }),
        ),

        // ملخص التقدم
        Obx(() {
          final completedCount = controller.taskSubtasks
              .where((s) => s.isCompleted)
              .length;
          final totalCount = controller.taskSubtasks.length;

          if (totalCount == 0) {
            return const SizedBox.shrink();
          }

          final progress = completedCount / totalCount;

          return Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.surface,
              boxShadow: [
                BoxShadow(
                  color: AppColors.getShadowColor(0.1),
                  blurRadius: 5,
                  offset: const Offset(0, -2),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'التقدم: $completedCount من $totalCount مكتملة',
                      style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.textPrimary,
                        fontWeight: FontWeight.w600,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.1),
                            blurRadius: 0.5,
                            offset: const Offset(0, 0.5),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      '${(progress * 100).toInt()}%',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w800,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.15),
                            blurRadius: 0.8,
                            offset: const Offset(0, 0.8),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: progress,
                  backgroundColor: AppColors.progressBackground,
                  valueColor: AlwaysStoppedAnimation<Color>(AppColors.primary),
                  minHeight: 8,
                  borderRadius: BorderRadius.circular(4),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// بناء عنصر المهمة الفرعية
  Widget _buildSubtaskItem(Subtask subtask) {
      //الصلاحيات
    final permissionService = Get.find<UnifiedPermissionService>();

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // مربع الاختيار للإكمال
                SizedBox(
                  width: 40,
                  child: Checkbox(
                    value: subtask.isCompleted,
                    activeColor: AppColors.primary,
                    onChanged: (value) {
                      if (value != null) {
                        _toggleSubtaskCompletion(subtask, value);
                      }
                    },
                  ),
                ),

                // عنوان المهمة الفرعية
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.only(top: 12),
                    child: Text(
                      subtask.title,
                      style: AppStyles.bodyMedium.copyWith(
                        decoration: subtask.isCompleted
                            ? TextDecoration.lineThrough
                            : null,
                        color: subtask.isCompleted
                            ? AppColors.textSecondary
                            : AppColors.textPrimary,
                        fontWeight: subtask.isCompleted
                            ? FontWeight.w500
                            : FontWeight.w700,
                        shadows: subtask.isCompleted ? null : [
                          Shadow(
                            color: AppColors.getShadowColor(0.1),
                            blurRadius: 0.5,
                            offset: const Offset(0, 0.5),
                          ),
                        ],
                      ),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 2,
                    ),
                  ),
                ),

                // قائمة الخيارات
                SizedBox(
                  width: 40,
                  child: PopupMenuButton<String>(
                    icon: const Icon(Icons.more_vert),
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showEditSubtaskDialog(subtask);
                          break;
                        case 'delete':
                          _showDeleteSubtaskDialog(subtask);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      if (permissionService.canEditTask())
                        PopupMenuItem<String>(
                          value: 'edit',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              const Icon(Icons.edit, size: 18),
                              const SizedBox(width: 8),
                              Text('تعديل', style: TextStyle(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.w600,
                              )),
                            ],
                          ),
                        ),
                      if (permissionService.canDeleteTask())
                        PopupMenuItem<String>(
                          value: 'delete',
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(Icons.delete, size: 18, color: AppColors.statusCancelled),
                              const SizedBox(width: 8),
                              Text('حذف', style: TextStyle(
                                color: AppColors.statusCancelled,
                                fontWeight: FontWeight.w600,
                              )),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),

            // معلومات إضافية
            Padding(
              padding: const EdgeInsets.only(right: 40, left: 16, bottom: 8),
              child: Row(
                children: [
                  Icon(Icons.access_time, size: 14, color: AppColors.textSecondary),
                  const SizedBox(width: 4),
                  Text(
                    'تم الإنشاء: ${subtask.createdAtDateTime.day}/${subtask.createdAtDateTime.month}/${subtask.createdAtDateTime.year}',
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.w600,
                      shadows: [
                        Shadow(
                          color: AppColors.getShadowColor(0.08),
                          blurRadius: 0.3,
                          offset: const Offset(0, 0.3),
                        ),
                      ],
                    ),
                  ),
                  if (subtask.isCompleted && subtask.completedAtDateTime != null) ...[
                    const SizedBox(width: 16),
                    Icon(Icons.check_circle, size: 14, color: AppColors.statusCompleted),
                    const SizedBox(width: 4),
                    Text(
                      'مكتمل: ${subtask.completedAtDateTime!.day}/${subtask.completedAtDateTime!.month}/${subtask.completedAtDateTime!.year}',
                      style: AppStyles.labelSmall.copyWith(
                        color: AppColors.statusCompleted,
                        fontWeight: FontWeight.w600,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.08),
                            blurRadius: 0.3,
                            offset: const Offset(0, 0.3),
                          ),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// بناء عنصر المهمة الفرعية
  Widget _buildSubtaskItem(Subtask subtask) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        leading: Checkbox(
          value: subtask.isCompleted,
          onChanged: (value) {
            if (value != null) {
              _toggleSubtaskCompletion(subtask, value);
            }
          },
        ),
        title: Text(
          subtask.title,
          style: TextStyle(
            decoration: subtask.isCompleted ? TextDecoration.lineThrough : null,
            color: subtask.isCompleted ? AppColors.textSecondary : AppColors.textPrimary,
          ),
        ),
        trailing: PopupMenuButton<String>(
          onSelected: (value) {
            if (value == 'delete') {
              _deleteSubtask(subtask);
            }
          },
          itemBuilder: (context) => [
            const PopupMenuItem(
              value: 'delete',
              child: Text('حذف'),
            ),
          ],
        ),
      ),
    );
  }

  /// حذف مهمة فرعية
  void _deleteSubtask(Subtask subtask) {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف المهمة الفرعية "${subtask.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              Get.back();
              final success = await controller.deleteSubtask(subtask.id);
              if (success) {
                Get.snackbar(
                  'تم بنجاح',
                  'تم حذف المهمة الفرعية بنجاح',
                  snackPosition: SnackPosition.BOTTOM,
                  backgroundColor: AppColors.success.withAlpha(51),
                  colorText: AppColors.success,
                );
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار تعديل مهمة فرعية
  void _showEditSubtaskDialog(Subtask subtask) {
    _titleController.text = subtask.title;
    Get.dialog(
      AlertDialog(
        title: const Text('تعديل المهمة الفرعية'),
        content: TextField(
          controller: _titleController,
          decoration: const InputDecoration(
            labelText: 'العنوان',
            border: OutlineInputBorder(),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (_titleController.text.trim().isEmpty) return;

              final updatedSubtask = subtask.copyWith(title: _titleController.text.trim());
              final success = await controller.updateSubtask(subtask.id, updatedSubtask);

              Get.back();
              if (success) {
                Get.snackbar('تم بنجاح', 'تم تعديل المهمة الفرعية');
              }
            },
            child: const Text('حفظ'),
          ),
        ],
      ),
    );
  }

  /// عرض مربع حوار حذف مهمة فرعية
  void _showDeleteSubtaskDialog(Subtask subtask) {
    Get.dialog(
      AlertDialog(
        title: const Text('حذف المهمة الفرعية'),
        content: Text('هل أنت متأكد من حذف "${subtask.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: () async {
              final success = await controller.deleteSubtask(subtask.id);
              Get.back();
              if (success) {
                Get.snackbar('تم بنجاح', 'تم حذف المهمة الفرعية');
              }
            },
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }
}
