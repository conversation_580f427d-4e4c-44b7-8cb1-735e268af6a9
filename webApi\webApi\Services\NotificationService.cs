using Microsoft.AspNetCore.SignalR;
using Microsoft.EntityFrameworkCore;
using webApi.Hubs;
using webApi.Models;

namespace webApi.Services
{
    /// <summary>
    /// خدمة الإشعارات - تدير إنشاء وإرسال الإشعارات
    /// </summary>
    public class NotificationService : INotificationService
    {
        private readonly TasksDbContext _context;
        private readonly IHubContext<NotificationHub> _notificationHubContext;
        private readonly ILogger<NotificationService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public NotificationService(
            TasksDbContext context,
            IHubContext<NotificationHub> notificationHubContext,
            ILogger<NotificationService> logger,
            IServiceProvider serviceProvider)
        {
            _context = context;
            _notificationHubContext = notificationHubContext;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// إنشاء وإرسال إشعار لمستخدم محدد
        /// </summary>
        public async Task<Notification> CreateAndSendNotificationAsync(int userId, string title, string content, string type, int? relatedId = null)
        {
            try
            {
                // التحقق من وجود المستخدم
                var user = await _context.Users.FindAsync(userId);
                if (user == null)
                {
                    _logger.LogWarning("محاولة إنشاء إشعار لمستخدم غير موجود: {UserId}", userId);
                    throw new ArgumentException($"المستخدم برقم {userId} غير موجود");
                }

                // إنشاء الإشعار
                var notification = new Notification
                {
                    UserId = userId,
                    Title = title,
                    Content = content,
                    Type = type,
                    RelatedId = relatedId,
                    IsRead = false,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                // حفظ الإشعار في قاعدة البيانات
                _context.Notifications.Add(notification);
                await _context.SaveChangesAsync();

                // إرسال الإشعار عبر SignalR
                await SendNotificationAsync(notification);

                _logger.LogInformation("تم إنشاء وإرسال إشعار للمستخدم {UserId}: {Title}", userId, title);
                return notification;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء وإرسال إشعار للمستخدم {UserId}", userId);
                throw;
            }
        }

        /// <summary>
        /// إنشاء وإرسال إشعار لمجموعة من المستخدمين
        /// </summary>
        public async Task<List<Notification>> CreateAndSendNotificationsAsync(List<int> userIds, string title, string content, string type, int? relatedId = null)
        {
            try
            {
                if (userIds == null || !userIds.Any())
                {
                    _logger.LogWarning("محاولة إنشاء إشعارات لقائمة مستخدمين فارغة");
                    return new List<Notification>();
                }

                // التحقق من وجود المستخدمين (محسن مع تحديد العدد الأقصى)
                var existingUserIds = await _context.Users
                    .Where(u => userIds.Contains(u.Id) && !u.IsDeleted)
                    .Take(100) // تحديد العدد الأقصى لتحسين الأداء
                    .Select(u => u.Id)
                    .ToListAsync();

                if (!existingUserIds.Any())
                {
                    _logger.LogWarning("لم يتم العثور على أي من المستخدمين المحددين");
                    return new List<Notification>();
                }

                var notifications = new List<Notification>();
                var currentTime = DateTimeOffset.UtcNow.ToUnixTimeSeconds();

                // إنشاء إشعار لكل مستخدم موجود
                foreach (var userId in existingUserIds)
                {
                    var notification = new Notification
                    {
                        UserId = userId,
                        Title = title,
                        Content = content,
                        Type = type,
                        RelatedId = relatedId,
                        IsRead = false,
                        CreatedAt = currentTime
                    };

                    notifications.Add(notification);
                }

                // حفظ الإشعارات في قاعدة البيانات
                _context.Notifications.AddRange(notifications);
                await _context.SaveChangesAsync();

                // إرسال الإشعارات عبر SignalR (محسن مع تجميع تحديثات العدد)
                var userIdsToUpdate = new HashSet<int>();

                foreach (var notification in notifications)
                {
                    // إرسال الإشعار فقط بدون تحديث العدد
                    await SendNotificationWithoutCountUpdateAsync(notification);
                    userIdsToUpdate.Add(notification.UserId);
                }

                // تحديث عدد الإشعارات لكل مستخدم مرة واحدة فقط
                foreach (var userId in userIdsToUpdate)
                {
                    await UpdateUnreadCountAsync(userId);
                }

                _logger.LogInformation("تم إنشاء وإرسال إشعارات لـ {Count} مستخدم: {Title}", notifications.Count, title);
                return notifications;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء وإرسال إشعارات لمجموعة مستخدمين");
                throw;
            }
        }

        /// <summary>
        /// إنشاء وإرسال إشعار لجميع المستخدمين
        /// </summary>
        public async Task<List<Notification>> CreateAndSendNotificationToAllAsync(string title, string content, string type, int? relatedId = null)
        {
            try
            {
                // الحصول على جميع معرفات المستخدمين
                var userIds = await _context.Users.Select(u => u.Id).ToListAsync();

                if (!userIds.Any())
                {
                    _logger.LogWarning("لا يوجد مستخدمين في النظام");
                    return new List<Notification>();
                }

                // استخدام الطريقة الموجودة لإنشاء وإرسال الإشعارات
                return await CreateAndSendNotificationsAsync(userIds, title, content, type, relatedId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إنشاء وإرسال إشعار لجميع المستخدمين");
                throw;
            }
        }

        /// <summary>
        /// إرسال إشعار موجود لمستخدم محدد
        /// </summary>
        public async Task<bool> SendNotificationAsync(Notification notification)
        {
            try
            {
                if (notification == null)
                {
                    _logger.LogWarning("محاولة إرسال إشعار فارغ");
                    return false;
                }

                // اسم المجموعة يكون بتنسيق User_{userId}
                var groupName = $"User_{notification.UserId}";

                // إرسال الإشعار عبر SignalR
                await _notificationHubContext.Clients.Group(groupName)
                    .SendAsync("ReceiveNotification", notification);

                // تحديث عدد الإشعارات غير المقروءة
                await UpdateUnreadCountAsync(notification.UserId);

                _logger.LogInformation("تم إرسال إشعار للمستخدم {UserId}: {Title}", notification.UserId, notification.Title);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعار للمستخدم {UserId}", notification.UserId);
                return false;
            }
        }

        /// <summary>
        /// إرسال إشعار بدون تحديث العدد (للاستخدام الداخلي)
        /// </summary>
        private async Task<bool> SendNotificationWithoutCountUpdateAsync(Notification notification)
        {
            try
            {
                if (notification == null)
                {
                    _logger.LogWarning("محاولة إرسال إشعار فارغ");
                    return false;
                }

                // اسم المجموعة يكون بتنسيق User_{userId}
                var groupName = $"User_{notification.UserId}";

                // إرسال الإشعار عبر SignalR فقط
                await _notificationHubContext.Clients.Group(groupName)
                    .SendAsync("ReceiveNotification", notification);

                _logger.LogInformation("تم إرسال إشعار للمستخدم {UserId}: {Title}", notification.UserId, notification.Title);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إرسال إشعار للمستخدم {UserId}", notification.UserId);
                return false;
            }
        }

        /// <summary>
        /// تحديث عدد الإشعارات غير المقروءة للمستخدم
        /// </summary>
        public async Task<int> UpdateUnreadCountAsync(int userId)
        {
            try
            {
                // استخدام DbContext منفصل لتجنب تضارب العمليات المتوازية
                using var scope = _serviceProvider.CreateScope();
                var separateContext = scope.ServiceProvider.GetRequiredService<TasksDbContext>();

                // الحصول على عدد الإشعارات غير المقروءة
                var unreadCount = await separateContext.Notifications
                    .CountAsync(n => n.UserId == userId && !n.IsRead);

                // اسم المجموعة يكون بتنسيق User_{userId}
                var groupName = $"User_{userId}";

                // إرسال التحديث عبر SignalR
                await _notificationHubContext.Clients.Group(groupName)
                    .SendAsync("UnreadCountUpdated", new { Count = unreadCount });

                _logger.LogInformation("تم تحديث عدد الإشعارات غير المقروءة للمستخدم {UserId}: {Count}", userId, unreadCount);
                return unreadCount;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث عدد الإشعارات غير المقروءة للمستخدم {UserId}", userId);
                return -1;
            }
        }
    }
}