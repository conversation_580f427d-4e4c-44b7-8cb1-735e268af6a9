import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:syncfusion_flutter_calendar/calendar.dart' as sf;
import 'package:syncfusion_flutter_core/theme.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/calendar_models.dart';
import '../../controllers/calendar_events_controller.dart';
import '../../services/unified_permission_service.dart';
import 'calendar_event_form.dart';
import 'calendar_event_details.dart';

/// مصدر بيانات مخصص لـ Syncfusion Calendar
class CalendarEventDataSource extends sf.CalendarDataSource {
  CalendarEventDataSource(List<CalendarEvent> source) {
    appointments = source;
  }

  @override
  DateTime getStartTime(int index) {
    return DateTime.fromMillisecondsSinceEpoch(appointments![index].startTime * 1000);
  }

  @override
  DateTime getEndTime(int index) {
    return DateTime.fromMillisecondsSinceEpoch(appointments![index].endTime * 1000);
  }

  @override
  String getSubject(int index) {
    return appointments![index].title;
  }

  @override
  Color getColor(int index) {
    final event = appointments![index] as CalendarEvent;
    return _parseColor(event.color) ?? _getEventTypeColor(event.eventType);
  }

  @override
  bool isAllDay(int index) {
    return appointments![index].allDay;
  }

  @override
  String? getNotes(int index) {
    return appointments![index].description;
  }

  @override
  Object? convertAppointmentToObject(
      Object? customData, sf.Appointment appointment) {
    // إذا كان لدينا بيانات مخصصة، نعيدها
    if (customData != null) {
      return customData;
    }

    // إنشاء CalendarEvent جديد من Appointment
    return CalendarEvent(
      id: (appointment.id is int) ? appointment.id as int : 0,
      title: appointment.subject,
      description: appointment.notes,
      startTime: appointment.startTime.millisecondsSinceEpoch ~/ 1000,
      endTime: appointment.endTime.millisecondsSinceEpoch ~/ 1000,
      allDay: appointment.isAllDay,
      color: '#${appointment.color.value.toRadixString(16).padLeft(8, '0').substring(2)}',
      userId: 1, // استخدام ID افتراضي
      createdAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      eventType: CalendarEventType.other,
    );
  }

  @override
  String? getLocation(int index) {
    return appointments![index].location;
  }

  /// تحليل اللون من النص
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  /// الحصول على لون نوع الحدث
  Color _getEventTypeColor(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return AppColors.info;
      case CalendarEventType.reminder:
        return AppColors.warning;
      case CalendarEventType.vacation:
        return AppColors.success;
      case CalendarEventType.other:
        return AppColors.info;
      default:
        return AppColors.primary;
    }
  }
}

/// شاشة التقويم المحسنة باستخدام Syncfusion Calendar
class CalendarScreen extends StatefulWidget {
  const CalendarScreen({super.key});

  @override
  State<CalendarScreen> createState() => _CalendarScreenState();
}

class _CalendarScreenState extends State<CalendarScreen> {
  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  late CalendarEventsController _calendarController;

  // كونترولر Syncfusion Calendar
  late sf.CalendarController _syncfusionCalendarController;

  // التاريخ المحدد
  late DateTime _selectedDay;

  // عرض التقويم الحالي
  sf.CalendarView _calendarView = sf.CalendarView.month;

  // نص البحث
  final TextEditingController _searchController = TextEditingController();

  // مصدر بيانات التقويم
  late CalendarEventDataSource _calendarDataSource;

  // متغيرات لتتبع حالة السحب والإفلات
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    // تهيئة متغيرات التقويم
    _selectedDay = DateTime.now();
    _syncfusionCalendarController = sf.CalendarController();
    _syncfusionCalendarController.selectedDate = _selectedDay;
    _syncfusionCalendarController.displayDate = _selectedDay;

    // الحصول على وحدة تحكم التقويم من الـ binding
    _calendarController = Get.find<CalendarEventsController>();

    // تهيئة مصدر البيانات
    _calendarDataSource = CalendarEventDataSource([]);

    // تحميل الأحداث عند تهيئة الشاشة
    _loadEvents();

    // تحميل تفضيلات العرض المحفوظة
    _loadSavedViewPreference();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _syncfusionCalendarController.dispose();
    super.dispose();
  }

  /// تحميل الأحداث والمهام
  Future<void> _loadEvents() async {
    debugPrint('🔄 تحميل الأحداث والمهام في التقويم...');

    // تحميل الأحداث العادية والمهام معاً
    await _calendarController.refreshEventsWithTasks();

    // تحديث مصدر البيانات
    _updateCalendarDataSourceWithState();

    debugPrint('✅ تم تحميل ${_calendarController.allEvents.length} حدث/مهمة في التقويم');
  }

  /// تحديث مصدر بيانات التقويم
  void _updateCalendarDataSource() {
    // تحديث مصدر البيانات بدون setState لتجنب مشاكل البناء
    _calendarDataSource = CalendarEventDataSource(_calendarController.allEvents);
  }

  /// تحديث مصدر بيانات التقويم مع setState
  void _updateCalendarDataSourceWithState() {
    setState(() {
      _calendarDataSource = CalendarEventDataSource(_calendarController.allEvents);
    });
  }

  /// تحديث نوع عرض التقويم
  void _updateCalendarView(sf.CalendarView newView) {
    setState(() {
      _calendarView = newView;
      _syncfusionCalendarController.view = newView;
    });

    // حفظ التفضيل
    _saveViewPreference(newView);

    // إجبار إعادة بناء التقويم بعد الإطار الحالي
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        setState(() {
          // تحديث مصدر البيانات لضمان التطبيق
          _calendarDataSource = CalendarEventDataSource(_calendarController.allEvents);
        });
      }
    });
  }

  /// تحميل تفضيلات العرض المحفوظة
  Future<void> _loadSavedViewPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedViewIndex = prefs.getInt('calendar_view_preference') ?? 0;

      final viewTypes = [
        sf.CalendarView.month,
        sf.CalendarView.week,
        sf.CalendarView.workWeek,
        sf.CalendarView.day,
        sf.CalendarView.schedule,
        sf.CalendarView.timelineDay,
        sf.CalendarView.timelineWeek,
      ];

      if (savedViewIndex < viewTypes.length) {
        setState(() {
          _calendarView = viewTypes[savedViewIndex];
          // تحديث كونترولر التقويم
          _syncfusionCalendarController.view = _calendarView;
        });
      }
    } catch (e) {
      debugPrint('خطأ في تحميل تفضيلات العرض: $e');
    }
  }

  /// حفظ تفضيلات العرض
  Future<void> _saveViewPreference(sf.CalendarView viewType) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      final viewTypes = [
        sf.CalendarView.month,
        sf.CalendarView.week,
        sf.CalendarView.workWeek,
        sf.CalendarView.day,
        sf.CalendarView.schedule,
        sf.CalendarView.timelineDay,
        sf.CalendarView.timelineWeek,
      ];

      final index = viewTypes.indexOf(viewType);
      if (index != -1) {
        await prefs.setInt('calendar_view_preference', index);
      }
    } catch (e) {
      debugPrint('خطأ في حفظ تفضيلات العرض: $e');
    }
  }

  /// إعادة تعيين تفضيلات العرض
  Future<void> _resetViewPreferences() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('calendar_view_preference');

      setState(() {
        _calendarView = sf.CalendarView.month; // العرض الافتراضي
        // تحديث كونترولر التقويم
        _syncfusionCalendarController.view = _calendarView;
      });
    } catch (e) {
      debugPrint('خطأ في إعادة تعيين تفضيلات العرض: $e');
    }
  }

  /// الحصول على اسم عرض التقويم
  String _getCalendarViewName(sf.CalendarView view) {
    if (view == sf.CalendarView.month) return 'شهر';
    if (view == sf.CalendarView.week) return 'أسبوع';
    if (view == sf.CalendarView.workWeek) return 'أسبوع العمل';
    if (view == sf.CalendarView.day) return 'يوم';
    if (view == sf.CalendarView.timelineDay) return 'جدول اليوم';
    if (view == sf.CalendarView.timelineWeek) return 'جدول الأسبوع';
    if (view == sf.CalendarView.timelineWorkWeek) return 'جدول أسبوع العمل';
    if (view == sf.CalendarView.timelineMonth) return 'جدول الشهر';
    if (view == sf.CalendarView.schedule) return 'الجدولة';
    return 'شهر';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('التقويم'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          // زر تغيير عرض التقويم
          IconButton(
            icon: const Icon(Icons.view_agenda),
            tooltip: 'تغيير نوع العرض',
            onPressed: _showViewTypeMenu,
          ),
          // زر المزامنة
          if (_permissionService.canManageCalendar())
            IconButton(
              icon: const Icon(Icons.sync),
              tooltip: 'مزامنة المهام',
              onPressed: _syncTasksToEvents,
            ),
          // زر إضافة حدث جديد
          if (_permissionService.canCreateCalendarEvents())
            IconButton(
              icon: const Icon(Icons.add),
              tooltip: 'إضافة حدث جديد',
              onPressed: () => _showAddEventDialog(),
            ),
          // زر البحث
          if (_permissionService.canManageCalendar())
            IconButton(
              icon: const Icon(Icons.search),
              tooltip: 'بحث في الأحداث',
              onPressed: _showSearchDialog,
            ),
          // قائمة المزيد من الخيارات
          if (_permissionService.canManageCalendar())
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert),
              tooltip: 'المزيد من الخيارات',
              onSelected: (value) {
                switch (value) {
                  case 'export':
                    _exportCalendar();
                    break;
                  case 'print':
                    _printCalendar();
                    break;
                  case 'settings':
                    _showCalendarSettings();
                    break;
                  case 'statistics':
                    _showCalendarStatistics();
                    break;
                }
              },
              itemBuilder: (context) => [
                if (_permissionService.canExportCalendar())
                  const PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        Icon(Icons.download),
                        SizedBox(width: 8),
                        Text('تصدير التقويم'),
                      ],
                    ),
                  ),
                if (_permissionService.canPrintCalendar())
                  const PopupMenuItem(
                    value: 'print',
                    child: Row(
                      children: [
                        Icon(Icons.print),
                        SizedBox(width: 8),
                        Text('طباعة التقويم'),
                      ],
                    ),
                  ),
                const PopupMenuItem(
                  value: 'settings',
                  child: Row(
                    children: [
                      Icon(Icons.settings),
                      SizedBox(width: 8),
                      Text('إعدادات التقويم'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'statistics',
                  child: Row(
                    children: [
                      Icon(Icons.analytics),
                      SizedBox(width: 8),
                      Text('إحصائيات التقويم'),
                    ],
                  ),
                ),
              ],
            ),
        ],
      ),
      body: Column(
        children: [
          // شريط الإحصائيات المصغرة
          _buildStatsBar(),

          // جسم التقويم
          Expanded(
            child: GetBuilder<CalendarEventsController>(
              builder: (controller) {
                // عرض مؤشر التحميل إذا كانت البيانات قيد التحميل
                if (controller.isLoading) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                // عرض رسالة الخطأ إذا كان هناك خطأ
                if (controller.error.isNotEmpty) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.error_outline, size: 48, color: AppColors.error),
                        const SizedBox(height: 16),
                        Text(
                          controller.error,
                          style: AppStyles.bodyLarge,
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: _loadEvents,
                          child: const Text('إعادة المحاولة'),
                        ),
                      ],
                    ),
                  );
                }

                // عرض التقويم
                return _buildCalendarView(controller);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// بناء عرض التقويم
  Widget _buildCalendarView(CalendarEventsController controller) {
    // تحديث مصدر البيانات بطريقة آمنة
    _updateCalendarDataSource();

    return Column(
      children: [
        // رأس التقويم مع أزرار التنقل
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              // زر تغيير عرض التقويم
              TextButton(
                onPressed: () {
                  sf.CalendarView newView;
                  if (_calendarView == sf.CalendarView.month) {
                    newView = sf.CalendarView.week;
                  } else if (_calendarView == sf.CalendarView.week) {
                    newView = sf.CalendarView.workWeek;
                  } else if (_calendarView == sf.CalendarView.workWeek) {
                    newView = sf.CalendarView.day;
                  } else {
                    newView = sf.CalendarView.month;
                  }

                  // تحديث العرض باستخدام الدالة المحسنة
                  _updateCalendarView(newView);
                },
                child: Text(
                  _getCalendarViewName(_calendarView),
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              // زر العودة إلى اليوم الحالي
              TextButton(
                onPressed: () {
                  setState(() {
                    _selectedDay = DateTime.now();
                    _syncfusionCalendarController.selectedDate = _selectedDay;
                    _syncfusionCalendarController.displayDate = _selectedDay;
                  });
                },
                child: Text(
                  'اليوم',
                  style: TextStyle(
                    color: Theme.of(context).primaryColor,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
        ),

        // التقويم مع ارتفاع محدد لمنع overflow
        Flexible(
          child: SfCalendarTheme(
            data: _buildCalendarTheme(),
            child: sf.SfCalendar(
            key: ValueKey('calendar_${_calendarView.toString()}'),
            controller: _syncfusionCalendarController,
            view: _calendarView,
            dataSource: _calendarDataSource,
            firstDayOfWeek: 6, // السبت هو أول يوم في الأسبوع
            showNavigationArrow: true,
            showDatePickerButton: true,
            allowDragAndDrop: true,
            allowAppointmentResize: true,
            showCurrentTimeIndicator: true,
            showTodayButton: true,
            showWeekNumber: true,
            cellEndPadding: 5,

            // أيام العطل والتواريخ المحظورة
            blackoutDates: _getBlackoutDates(),
            blackoutDatesTextStyle: AppStyles.bodySmall.copyWith(
              color: AppColors.error.withAlpha(150),
              decoration: TextDecoration.lineThrough,
              fontWeight: FontWeight.w400,
            ),

            // تخصيص مظهر اليوم الحالي
            todayHighlightColor: Theme.of(context).primaryColor,
            todayTextStyle: AppStyles.bodyMedium.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
            

            // إعدادات التقويم المحسنة
            monthViewSettings: sf.MonthViewSettings(
              appointmentDisplayMode: sf.MonthAppointmentDisplayMode.appointment,
              appointmentDisplayCount: 4,
              showAgenda: false,
              agendaViewHeight: 200,
              agendaItemHeight: 60,
              navigationDirection: sf.MonthNavigationDirection.horizontal,
              showTrailingAndLeadingDates: true,
              numberOfWeeksInView: 6,
              dayFormat: 'EEE',

              // تخصيص مظهر خلايا الشهر
              monthCellStyle: sf.MonthCellStyle(
                backgroundColor: Colors.transparent,
                todayBackgroundColor: Theme.of(context).primaryColor.withAlpha(100),
                leadingDatesBackgroundColor: AppColors.surface.withAlpha(30),
                trailingDatesBackgroundColor: AppColors.surface.withAlpha(30),
                textStyle: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
                // todayTextStyle تم نقله إلى SfCalendar الرئيسي
                leadingDatesTextStyle: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary.withAlpha(120),
                  fontWeight: FontWeight.w400,
                ),
                trailingDatesTextStyle: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary.withAlpha(120),
                  fontWeight: FontWeight.w400,
                ),
              ),

              // تخصيص مظهر عرض الأجندة
              agendaStyle: sf.AgendaStyle(
                backgroundColor: AppColors.surface,
                appointmentTextStyle: AppStyles.bodyMedium.copyWith(
                  fontWeight: FontWeight.w500,
                  color: AppColors.textPrimary,
                ),
                dayTextStyle: AppStyles.headingSmall.copyWith(
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).primaryColor,
                ),
                dateTextStyle: AppStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimary,
                ),
              ),
            ),

            // إعدادات عرض الأسبوع المحسنة
            timeSlotViewSettings: sf.TimeSlotViewSettings(
              startHour: 6,
              endHour: 22,
              nonWorkingDays: const <int>[DateTime.friday, DateTime.saturday],
              timeInterval: const Duration(minutes: 30),
              timeIntervalHeight: 60,
              timeFormat: 'HH:mm',
              dateFormat: 'dd/MM',
              dayFormat: 'EEE',
              timeRulerSize: 60,
              minimumAppointmentDuration: const Duration(minutes: 15),

              // تخصيص مظهر الشبكة الزمنية
              timeTextStyle: AppStyles.labelSmall.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),

            // إعدادات عرض الجدولة
            scheduleViewSettings: sf.ScheduleViewSettings(
              appointmentItemHeight: 70,
              hideEmptyScheduleWeek: true,
              monthHeaderSettings: sf.MonthHeaderSettings(
                monthFormat: 'MMMM yyyy',
                height: 100,
                textAlign: TextAlign.center,
                backgroundColor: Theme.of(context).primaryColor.withAlpha(50),
                monthTextStyle: AppStyles.headingMedium.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
              weekHeaderSettings: sf.WeekHeaderSettings(
                startDateFormat: 'dd MMM',
                endDateFormat: 'dd MMM',
                height: 50,
                textAlign: TextAlign.center,
                backgroundColor: AppColors.surface,
                weekTextStyle: AppStyles.bodyLarge.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              dayHeaderSettings: sf.DayHeaderSettings(
                dayFormat: 'EEE',
                width: 70,
                dayTextStyle: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                  fontWeight: FontWeight.w500,
                ),
                dateTextStyle: AppStyles.bodyLarge.copyWith(
                  color: Theme.of(context).primaryColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),

            // إعدادات الرأس المحسنة
            headerStyle: sf.CalendarHeaderStyle(
              textAlign: TextAlign.center,
              backgroundColor: Colors.transparent,
              textStyle: AppStyles.headingMedium.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),

            // إعدادات عرض الجدول الزمني محددة في السمة

            // إعدادات رأس العرض
            viewHeaderStyle: sf.ViewHeaderStyle(
              backgroundColor: AppColors.surface,
              dayTextStyle: AppStyles.bodyMedium.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.w600,
              ),
              dateTextStyle: AppStyles.bodyLarge.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),

            // معالجات الأحداث
            onTap: (sf.CalendarTapDetails details) {
              if (details.targetElement == sf.CalendarElement.calendarCell) {
                setState(() {
                  _selectedDay = details.date!;
                  _syncfusionCalendarController.selectedDate = _selectedDay;
                });
              } else if (details.targetElement == sf.CalendarElement.appointment) {
                if (details.appointments != null && details.appointments!.isNotEmpty) {
                  final event = details.appointments!.first as CalendarEvent;
                  _handleEventTap(event);
                }
              }
            },

            onLongPress: (sf.CalendarLongPressDetails details) {
              if (details.targetElement == sf.CalendarElement.calendarCell) {
                if (_permissionService.canCreateCalendarEvents()) {
                  _showAddEventDialog(selectedDate: details.date);
                }
              }
            },

            onViewChanged: (sf.ViewChangedDetails details) {
              // تحديث البيانات عند تغيير العرض
              _loadEvents();
            },

            // معالجات السحب والإفلات
            onDragStart: (sf.AppointmentDragStartDetails details) {
              _onDragStart(details);
            },

            onDragUpdate: (sf.AppointmentDragUpdateDetails details) {
              _onDragUpdate(details);
            },

            onDragEnd: (sf.AppointmentDragEndDetails details) {
              _onDragEnd(details);
            },

            // المنشئون المخصصة
            appointmentBuilder: (context, calendarAppointmentDetails) {
              return _buildCustomAppointment(calendarAppointmentDetails);
            },

            monthCellBuilder: (context, details) {
              return _buildCustomMonthCell(details);
            },

            timeRegionBuilder: (context, timeRegionDetails) {
              return _buildTimeRegion(timeRegionDetails);
            },

            // إعدادات إضافية
            appointmentTextStyle: AppStyles.bodySmall.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
            backgroundColor: Colors.transparent,
            selectionDecoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withAlpha(51),
              border: Border.all(
                color: Theme.of(context).primaryColor,
                width: 2,
              ),
              borderRadius: BorderRadius.circular(4),
            ),
          ),
          ),
        ),

        // قائمة الأحداث لليوم المحدد
        Expanded(
          child: _buildEventsList(controller.getEventsForDay(_selectedDay)),
        ),
      ],
    );
  }

  /// معالج بداية السحب
  void _onDragStart(sf.AppointmentDragStartDetails details) {
    final event = details.appointment as CalendarEvent;

    // فحص الصلاحيات
    if (!_canEditEvent(event)) {
      Get.snackbar(
        'غير مسموح',
        'ليس لديك صلاحية لتعديل هذا الحدث',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.block, color: AppColors.white),
      );
      return;
    }

    // تحديث حالة السحب
    setState(() {
      _isDragging = true;
    });

    // إظهار مؤشر بداية السحب
    Get.snackbar(
      'جاري السحب 🎯',
      'اسحب "${event.title}" إلى التاريخ المطلوب',
      snackPosition: SnackPosition.TOP,
      backgroundColor: AppColors.info,
      colorText: AppColors.white,
      duration: const Duration(seconds: 1),
      icon: const Icon(Icons.touch_app, color: AppColors.white),
      margin: const EdgeInsets.all(8),
    );

    debugPrint('بدء سحب الحدث: ${event.title}');
  }

  /// معالج تحديث السحب
  void _onDragUpdate(sf.AppointmentDragUpdateDetails details) {
    // تحديث موقع السحب للمؤشرات البصرية
    setState(() {
    });

    debugPrint('تحديث موقع السحب: ${details.draggingPosition}');

    // إضافة ردود فعل لمسية
    // HapticFeedback.selectionClick();
  }

  /// معالج انتهاء السحب
  void _onDragEnd(sf.AppointmentDragEndDetails details) async {
    final event = details.appointment as CalendarEvent;
    final newStartTime = details.droppingTime;

    // فحص الصلاحيات مرة أخرى
    if (!_canEditEvent(event)) {
      return;
    }

    try {
      // حساب الفرق الزمني
      final originalStartTime = DateTime.fromMillisecondsSinceEpoch(event.startTime * 1000);
      final timeDifference = newStartTime!.difference(originalStartTime);

      // تحديث أوقات الحدث
      final newEndTime = DateTime.fromMillisecondsSinceEpoch(event.endTime * 1000).add(timeDifference);

      // إنشاء حدث محدث
      final updatedEvent = CalendarEvent(
        id: event.id,
        title: event.title,
        description: event.description,
        startTime: newStartTime.millisecondsSinceEpoch ~/ 1000,
        endTime: newEndTime.millisecondsSinceEpoch ~/ 1000,
        eventType: event.eventType,
        userId: event.userId,
        allDay: event.allDay,
        color: event.color,
        location: event.location,
        reminderMinutes: event.reminderMinutes,
        recurrenceRule: event.recurrenceRule,
        createdAt: event.createdAt,
        updatedAt: DateTime.now().millisecondsSinceEpoch ~/ 1000,
        duration: event.duration,
        recurrencePattern: event.recurrencePattern,
        recurrenceCount: event.recurrenceCount,
        recurrenceEndDate: event.recurrenceEndDate,
        reminderTime: event.reminderTime,
        isReminderEnabled: event.isReminderEnabled,
        taskId: event.taskId,
        isDeleted: event.isDeleted,
      );

      // تحديث الحدث في قاعدة البيانات
      await _calendarController.updateEvent(event.id, updatedEvent);

      // إعادة تحميل الأحداث
      await _loadEvents();

      // إظهار رسالة نجاح مع تفاصيل التحديث
      final formattedDate = DateFormat('dd/MM/yyyy').format(newStartTime);
      final formattedTime = DateFormat('HH:mm').format(newStartTime);

      Get.snackbar(
        'تم الإفلات بنجاح ✅',
        'تم نقل "${event.title}" إلى $formattedDate في $formattedTime',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.event_available, color: AppColors.white),
        margin: const EdgeInsets.all(16),
        borderRadius: 8,
      );

    } catch (e) {
      // إظهار رسالة خطأ
      Get.snackbar(
        'خطأ في التحديث',
        'فشل في نقل الحدث: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: AppColors.white),
      );

      // إعادة تحميل الأحداث لاستعادة الحالة الأصلية
      await _loadEvents();
    } finally {
      // إعادة تعيين حالة السحب
      setState(() {
        _isDragging = false;
      });
    }
  }

  /// فحص إمكانية تعديل الحدث
  bool _canEditEvent(CalendarEvent event) {
    final permissionService = Get.find<UnifiedPermissionService>();

    // فحص الصلاحيات العامة
    if (!permissionService.canEditCalendarEvents()) {
      return false;
    }

    // السماح للمستخدم بتعديل أحداثه الخاصة
    // TODO: إضافة فحص المستخدم الحالي عند توفر AuthController
    // if (event.userId == currentUserId) {
    //   return true;
    // }

    // فحص صلاحيات الإدارة
    return permissionService.canManageCalendar();
  }

  /// فحص إمكانية الإفلات على تاريخ معين
  bool _canDropOnDate(DateTime date) {
    // منع الإفلات في الماضي (اختياري)
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final targetDate = DateTime(date.year, date.month, date.day);

    // السماح بالإفلات على التاريخ الحالي والمستقبل فقط
    if (targetDate.isBefore(today)) {
      return false;
    }

    // يمكن إضافة قواعد أخرى هنا مثل:
    // - منع الإفلات في أيام العطل
    // - منع الإفلات خارج أوقات العمل
    // - فحص توفر الوقت

    return true;
  }

  /// تحويل أولوية المهمة إلى نص عربي
  String _getTaskPriorityText(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  }

  /// تحويل حالة المهمة إلى نص عربي
  String _getTaskStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'معلقة';
      case 'in_progress':
        return 'جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }

  /// معالجة النقر على الأحداث/المهام
  void _handleEventTap(CalendarEvent event) {
    final isTask = event.eventType == CalendarEventType.task || event.taskId != null;

    if (isTask && event.taskId != null) {
      // إذا كانت مهمة، انتقل إلى تفاصيل المهمة
      _showTaskDetails(event.taskId!);
    } else {
      // إذا كان حدث عادي، اعرض تفاصيل الحدث
      _showEventDetails(event.id);
    }
  }

  /// عرض تفاصيل المهمة
  void _showTaskDetails(int taskId) {
    try {
      // الانتقال إلى شاشة تفاصيل المهمة
      Get.toNamed('/task-details', arguments: {'taskId': taskId});
    } catch (e) {
      debugPrint('❌ خطأ في عرض تفاصيل المهمة: $e');
      Get.snackbar(
        'خطأ',
        'لا يمكن عرض تفاصيل المهمة',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
    }
  }

  /// بناء حدث مخصص
  Widget _buildCustomAppointment(sf.CalendarAppointmentDetails details) {
    final event = details.appointments.first as CalendarEvent;
    final eventColor = _parseColor(event.color) ?? _getEventTypeColor(event.eventType);
    final isTask = event.eventType == CalendarEventType.task || event.taskId != null;
    final isSmall = details.bounds.height < 25;

    return Container(
      decoration: BoxDecoration(
        color: eventColor,
        borderRadius: BorderRadius.circular(isTask ? 6 : 4),
        border: Border.all(
          color: isTask ? eventColor.withAlpha(255) : eventColor.withAlpha(200),
          width: isTask ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color: eventColor.withAlpha(isTask ? 150 : 100),
            blurRadius: isTask ? 3 : 2,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Padding(
        padding: EdgeInsets.all(isSmall ? 1 : 2),
        child: isSmall
            ? _buildSimpleAppointment(event, isTask)
            : _buildDetailedAppointment(event, isTask, details),
      ),
    );
  }

  /// بناء حدث بسيط للمساحات الصغيرة
  Widget _buildSimpleAppointment(CalendarEvent event, bool isTask) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          isTask ? Icons.task_alt : _getEventTypeIcon(event.eventType),
          size: 8,
          color: AppColors.white,
        ),
        const SizedBox(width: 2),
        Expanded(
          child: Text(
            isTask ? '📋 ${event.title}' : event.title,
            style: AppStyles.labelSmall.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
              fontSize: 8,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
      ],
    );
  }

  /// بناء حدث مفصل للمساحات الكبيرة
  Widget _buildDetailedAppointment(CalendarEvent event, bool isTask, sf.CalendarAppointmentDetails details) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          isTask ? Icons.task_alt : _getEventTypeIcon(event.eventType),
          size: 10,
          color: AppColors.white,
        ),
        const SizedBox(width: 2),
        Expanded(
          child: isTask && event.task != null && details.bounds.height > 30
              ? Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Flexible(
                      child: Text(
                        '📋 ${event.title}',
                        style: AppStyles.labelSmall.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 9,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Flexible(
                      child: Text(
                        'أولوية: ${_getTaskPriorityText(event.task!.priority)}',
                        style: AppStyles.labelSmall.copyWith(
                          color: AppColors.white.withAlpha(180),
                          fontSize: 7,
                        ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                )
              : Text(
                  isTask ? '📋 ${event.title}' : event.title,
                  style: AppStyles.labelSmall.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 9,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
        ),
        if (!event.allDay && details.bounds.width > 80)
          Text(
            _formatTime(event.startTime),
            style: AppStyles.labelSmall.copyWith(
              color: AppColors.white.withAlpha(200),
              fontSize: 7,
            ),
          ),
      ],
    );
  }

  /// بناء خلية شهر مخصصة
  Widget _buildCustomMonthCell(sf.MonthCellDetails details) {
    final eventsForDay = _calendarController.getEventsForDay(details.date);
    final isToday = DateTime.now().day == details.date.day &&
                   DateTime.now().month == details.date.month &&
                   DateTime.now().year == details.date.year;
    final isSelected = _selectedDay.day == details.date.day &&
                      _selectedDay.month == details.date.month &&
                      _selectedDay.year == details.date.year;

    // تحديد ما إذا كانت هذه الخلية منطقة إفلات محتملة
    final isDropZone = _isDragging && _canDropOnDate(details.date);

    return Container(
      decoration: BoxDecoration(
        color: isDropZone
            ? AppColors.success.withAlpha(100) // لون مميز للمناطق القابلة للإفلات
            : isSelected
                ? Theme.of(context).primaryColor.withAlpha(51)
                : isToday
                    ? Theme.of(context).primaryColor.withAlpha(25)
                    : Colors.transparent,
        border: Border.all(
          color: isDropZone
              ? AppColors.success // حدود خضراء للمناطق القابلة للإفلات
              : isSelected
                  ? Theme.of(context).primaryColor
                  : AppColors.textSecondary.withAlpha(30),
          width: isDropZone ? 3 : (isSelected ? 2 : 0.5),
        ),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Stack(
        children: [
          // رقم اليوم
          Positioned(
            top: 4,
            right: 4,
            child: Container(
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: isToday
                    ? Theme.of(context).primaryColor
                    : Colors.transparent,
                shape: BoxShape.circle,
              ),
              child: Text(
                details.date.day.toString(),
                style: AppStyles.bodySmall.copyWith(
                  color: isToday
                      ? AppColors.white
                      : details.date.month == _selectedDay.month
                          ? AppColors.textPrimary
                          : AppColors.textSecondary.withAlpha(100),
                  fontWeight: isToday ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          ),

          // مؤشرات الأحداث
          if (eventsForDay.isNotEmpty)
            Positioned(
              bottom: 2,
              left: 2,
              right: 2,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  eventsForDay.length > 3 ? 3 : eventsForDay.length,
                  (index) {
                    final event = eventsForDay[index];
                    return Container(
                      margin: const EdgeInsets.symmetric(horizontal: 1),
                      width: 6,
                      height: 6,
                      decoration: BoxDecoration(
                        color: _getEventTypeColor(event.eventType),
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: AppColors.textPrimary,
                          width: 0.5,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),

          // عداد الأحداث الإضافية
          if (eventsForDay.length > 3)
            Positioned(
              bottom: 2,
              left: 2,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 1),
                decoration: BoxDecoration(
                  color: AppColors.textSecondary,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '+${eventsForDay.length - 3}',
                  style: AppStyles.labelSmall.copyWith(
                    // color: AppColors.primary,
                    // fontSize: 8,
                  ),
                ),
              ),
            ),

          // أيقونة الإفلات للمناطق المتاحة
          if (isDropZone)
            Positioned(
              top: 2,
              left: 2,
              child: Container(
                padding: const EdgeInsets.all(2),
                decoration: BoxDecoration(
                  color: AppColors.success,
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.success.withAlpha(100),
                      blurRadius: 4,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.add_circle_outline,
                  size: 12,
                  color: AppColors.white,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// بناء منطقة زمنية مخصصة
  Widget _buildTimeRegion(sf.TimeRegionDetails details) {
    return Container(
      decoration: BoxDecoration(
        color: details.region.color?.withAlpha(100) ?? AppColors.info.withAlpha(50),
        borderRadius: BorderRadius.circular(4),
        border: Border.all(
          color: details.region.color ?? AppColors.info,
          width: 1,
        ),
      ),
      child: Center(
        child: Text(
          details.region.text ?? 'منطقة إفلات',
          style: AppStyles.labelSmall.copyWith(
            color: details.region.color ?? AppColors.info,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }



  /// بناء سمة التقويم الموحدة
  SfCalendarThemeData _buildCalendarTheme() {
    return SfCalendarThemeData(
      // ألوان الخلفية
      backgroundColor: Colors.transparent,
      headerBackgroundColor: Colors.transparent,
      agendaBackgroundColor: Theme.of(context).scaffoldBackgroundColor,
      cellBorderColor: AppColors.textSecondary.withAlpha(30),

      // ألوان النصوص
      headerTextStyle: AppStyles.headingMedium.copyWith(
        color: Theme.of(context).primaryColor,
        fontWeight: FontWeight.bold,
      ),
      agendaDayTextStyle: AppStyles.bodyLarge.copyWith(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.bold,
      ),
      agendaDateTextStyle: AppStyles.bodySmall.copyWith(
        color: AppColors.textSecondary,
      ),

      // ألوان اليوم الحالي
      todayBackgroundColor: Theme.of(context).primaryColor.withAlpha(51),
      todayTextStyle: AppStyles.bodyMedium.copyWith(
        color: Theme.of(context).primaryColor,
        fontWeight: FontWeight.bold,
      ),

      // ألوان التحديد
      selectionBorderColor: Theme.of(context).primaryColor,

      // ألوان الخطوط الزمنية
      timeTextStyle: AppStyles.labelSmall.copyWith(
        color: AppColors.textSecondary,
      ),

      // ألوان أيام الأسبوع
      weekNumberTextStyle: AppStyles.labelSmall.copyWith(
        color: AppColors.textSecondary,
      ),

      // ألوان الحدود
      viewHeaderBackgroundColor: Colors.transparent,
      viewHeaderDayTextStyle: AppStyles.bodySmall.copyWith(
        color: AppColors.textPrimary,
        fontWeight: FontWeight.w600,
      ),
      viewHeaderDateTextStyle: AppStyles.bodySmall.copyWith(
        color: AppColors.textSecondary,
      ),

      // ألوان إضافية
      blackoutDatesTextStyle: AppStyles.bodySmall.copyWith(
        color: AppColors.textSecondary.withAlpha(100),
        decoration: TextDecoration.lineThrough,
      ),

      // ألوان الأيام الرائدة والتابعة
      leadingDatesTextStyle: AppStyles.bodySmall.copyWith(
        color: AppColors.textSecondary.withAlpha(100),
      ),
      trailingDatesTextStyle: AppStyles.bodySmall.copyWith(
        color: AppColors.textSecondary.withAlpha(100),
      ),
    );
  }

  /// بناء شريط الإحصائيات المصغرة
  Widget _buildStatsBar() {
    return Obx(() {
      final stats = _calendarController.eventStatistics;
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          children: [
            _buildStatItem('اليوم', '${stats['today']}', Icons.today, AppColors.primary),
            _buildStatItem('الأسبوع', '${stats['thisWeek']}', Icons.view_week, AppColors.info),
            _buildStatItem('الشهر', '${stats['thisMonth']}', Icons.calendar_month, AppColors.success),
            _buildStatItem('القادمة', '${stats['upcoming']}', Icons.schedule, AppColors.warning),
          ],
        ),
      );
    });
  }

  /// بناء عنصر إحصائي
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: AppStyles.bodyLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: color,
          ),
        ),
        Text(
          label,
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  /// عرض نموذج إضافة حدث جديد
  void _showAddEventDialog({DateTime? selectedDate}) {
    Get.to(() => CalendarEventForm(
      initialDate: selectedDate ?? _selectedDay,
      onEventCreated: (newEvent) {
        _loadEvents();
      },
    ));
  }

  /// عرض تفاصيل الحدث
  void _showEventDetails(int eventId) {
    // البحث عن الحدث في القائمة
    final event = _calendarController.allEvents.firstWhereOrNull(
      (e) => e.id == eventId,
    );

    if (event != null) {
      Get.to(() => CalendarEventDetails(
        event: event,
        onEventUpdated: () {
          _loadEvents();
          setState(() {}); // تحديث الواجهة
        },
        onEventDeleted: () {
          _loadEvents();
          setState(() {}); // تحديث الواجهة
        },
      ));
    } else {
      Get.snackbar(
        'خطأ',
        'لم يتم العثور على الحدث',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
    }
  }

  /// عرض قائمة تغيير نوع العرض المحسنة
  void _showViewTypeMenu() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: BoxDecoration(
          color: Theme.of(context).scaffoldBackgroundColor,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          boxShadow: [
            BoxShadow(
              color: AppColors.getShadowColor(0.2),
              blurRadius: 10,
              spreadRadius: 2,
            ),
          ],
        ),
        child: ConstrainedBox(
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
            // مقبض السحب
            Container(
              margin: const EdgeInsets.only(top: 12),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: AppColors.textSecondary.withAlpha(100),
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // العنوان
            Padding(
              padding: const EdgeInsets.all(20),
              child: Row(
                children: [
                  Icon(
                    Icons.view_agenda,
                    color: Theme.of(context).primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'اختر نوع العرض',
                    style: AppStyles.headingMedium.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withAlpha(51),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withAlpha(100),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      'الحالي: ${_getCalendarViewName(_calendarView)}',
                      style: AppStyles.bodySmall.copyWith(
                        color: Theme.of(context).primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // زر إعادة تعيين التفضيلات
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () {
                        _resetViewPreferences();
                        Navigator.pop(context);
                        Get.snackbar(
                          'تم إعادة التعيين',
                          'تم إعادة تعيين تفضيلات العرض إلى الافتراضي',
                          snackPosition: SnackPosition.BOTTOM,
                          backgroundColor: AppColors.info,
                          colorText: AppColors.white,
                          duration: const Duration(seconds: 2),
                          margin: const EdgeInsets.all(16),
                          borderRadius: 8,
                          icon: const Icon(Icons.refresh, color: AppColors.white),
                        );
                      },
                      icon: const Icon(Icons.refresh, size: 16),
                      label: const Text('إعادة تعيين التفضيلات'),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppColors.textSecondary,
                        side: BorderSide(color: AppColors.textSecondary.withAlpha(100)),
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // قائمة أنواع العرض مع سكرول
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                  _buildViewTypeCard(
                    sf.CalendarView.month,
                    'عرض الشهر',
                    'عرض شامل للشهر الكامل مع جميع الأحداث',
                    Icons.calendar_month,
                    AppColors.primary,
                  ),
                  const SizedBox(height: 8),
                  _buildViewTypeCard(
                    sf.CalendarView.week,
                    'عرض الأسبوع',
                    'عرض تفصيلي للأسبوع مع الساعات',
                    Icons.view_week,
                    AppColors.info,
                  ),
                  const SizedBox(height: 8),
                  _buildViewTypeCard(
                    sf.CalendarView.workWeek,
                    'أسبوع العمل',
                    'عرض أيام العمل فقط (السبت - الخميس)',
                    Icons.work,
                    AppColors.warning,
                  ),
                  const SizedBox(height: 8),
                  _buildViewTypeCard(
                    sf.CalendarView.day,
                    'عرض اليوم',
                    'عرض مفصل ليوم واحد بالساعات',
                    Icons.view_day,
                    AppColors.success,
                  ),
                  const SizedBox(height: 8),
                  _buildViewTypeCard(
                    sf.CalendarView.schedule,
                    'عرض الجدولة',
                    'قائمة مرتبة زمنياً بجميع الأحداث',
                    Icons.schedule,
                    AppColors.error,
                  ),
                  const SizedBox(height: 8),
                  _buildViewTypeCard(
                    sf.CalendarView.timelineDay,
                    'الجدول الزمني اليومي',
                    'عرض الأحداث على خط زمني لليوم',
                    Icons.timeline,
                    Colors.purple,
                  ),
                  const SizedBox(height: 8),
                  _buildViewTypeCard(
                    sf.CalendarView.timelineWeek,
                    'الجدول الزمني الأسبوعي',
                    'عرض الأحداث على خط زمني للأسبوع',
                    Icons.view_timeline,
                    Colors.orange,
                  ),
                    const SizedBox(height: 20),
                  ],
                ),
              ),
            ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء بطاقة نوع العرض المحسنة
  Widget _buildViewTypeCard(
    sf.CalendarView viewType,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    final isSelected = _calendarView == viewType;

    return AnimatedContainer(
      duration: const Duration(milliseconds: 200),
      decoration: BoxDecoration(
        color: isSelected
            ? color.withAlpha(51)
            : Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? color
              : AppColors.textSecondary.withAlpha(50),
          width: isSelected ? 2 : 1,
        ),
        boxShadow: isSelected ? [
          BoxShadow(
            color: color.withAlpha(100),
            blurRadius: 8,
            spreadRadius: 1,
            offset: const Offset(0, 2),
          ),
        ] : [
          BoxShadow(
            color: AppColors.getShadowColor(0.05),
            blurRadius: 4,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(12),
          onTap: () {
            // تحديث نوع العرض
            _updateCalendarView(viewType);

            // إغلاق القائمة
            Navigator.pop(context);

            // إظهار رسالة تأكيد
            Get.snackbar(
              'تم تغيير العرض',
              'تم تطبيق $title بنجاح وحفظ كتفضيل',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: color,
              colorText: AppColors.white,
              duration: const Duration(seconds: 2),
              margin: const EdgeInsets.all(16),
              borderRadius: 8,
              icon: Icon(icon, color: AppColors.white),
            );
          },
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                // أيقونة نوع العرض مع معاينة
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: color.withAlpha(isSelected ? 100 : 51),
                    borderRadius: BorderRadius.circular(10),
                    border: isSelected ? Border.all(
                      color: color.withAlpha(100),
                      width: 1,
                    ) : null,
                  ),
                  child: Stack(
                    children: [
                      Icon(
                        icon,
                        color: isSelected ? color : color.withAlpha(180),
                        size: 24,
                      ),
                      if (isSelected)
                        Positioned(
                          right: -2,
                          top: -2,
                          child: Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: color,
                              shape: BoxShape.circle,
                              border: Border.all(
                                color: AppColors.white,
                                width: 1,
                              ),
                            ),
                          ),
                        ),
                    ],
                  ),
                ),

                const SizedBox(width: 16),

                // معلومات نوع العرض
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            title,
                            style: AppStyles.bodyLarge.copyWith(
                              fontWeight: FontWeight.bold,
                              color: isSelected
                                  ? color
                                  : AppColors.textPrimary,
                            ),
                          ),
                          if (isSelected) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: color,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                'مفعل',
                                style: AppStyles.labelSmall.copyWith(
                                  color: AppColors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textSecondary,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 6),
                      // إحصائيات سريعة
                      Row(
                        children: [
                          _buildQuickStat(
                            Icons.visibility,
                            _getViewTypeDescription(viewType),
                            color.withAlpha(150),
                          ),
                          const SizedBox(width: 12),
                          _buildQuickStat(
                            Icons.schedule,
                            _getViewTypeDuration(viewType),
                            AppColors.textSecondary,
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // سهم الاختيار
                Icon(
                  isSelected
                      ? Icons.check_circle
                      : Icons.arrow_forward_ios,
                  color: isSelected
                      ? color
                      : AppColors.textSecondary,
                  size: isSelected ? 24 : 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء إحصائية سريعة
  Widget _buildQuickStat(IconData icon, String text, Color color) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 12,
          color: color,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: AppStyles.labelSmall.copyWith(
            color: color,
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  /// الحصول على وصف نوع العرض
  String _getViewTypeDescription(sf.CalendarView viewType) {
    switch (viewType) {
      case sf.CalendarView.month:
        return 'شامل';
      case sf.CalendarView.week:
        return 'تفصيلي';
      case sf.CalendarView.workWeek:
        return 'عملي';
      case sf.CalendarView.day:
        return 'مركز';
      case sf.CalendarView.schedule:
        return 'مرتب';
      case sf.CalendarView.timelineDay:
        return 'زمني';
      case sf.CalendarView.timelineWeek:
        return 'خطي';
      default:
        return 'عام';
    }
  }

  /// الحصول على مدة نوع العرض
  String _getViewTypeDuration(sf.CalendarView viewType) {
    switch (viewType) {
      case sf.CalendarView.month:
        return '30 يوم';
      case sf.CalendarView.week:
        return '7 أيام';
      case sf.CalendarView.workWeek:
        return '5 أيام';
      case sf.CalendarView.day:
        return '24 ساعة';
      case sf.CalendarView.schedule:
        return 'مرن';
      case sf.CalendarView.timelineDay:
        return '1 يوم';
      case sf.CalendarView.timelineWeek:
        return '1 أسبوع';
      default:
        return 'متغير';
    }
  }

  /// مزامنة المهام مع أحداث التقويم
  Future<void> _syncTasksToEvents() async {
    try {
      debugPrint('🔄 بدء مزامنة المهام مع التقويم...');

      // مزامنة المهام مع التقويم
      await _calendarController.syncTasksWithCalendar();

      // تحديث مصدر البيانات
      _updateCalendarDataSourceWithState();

      Get.snackbar(
        'تمت المزامنة ✅',
        'تم تحديث أحداث التقويم والمهام بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
        duration: const Duration(seconds: 2),
        icon: const Icon(Icons.sync, color: AppColors.white),
      );

      debugPrint('✅ تمت مزامنة ${_calendarController.allEvents.length} حدث/مهمة');
    } catch (e) {
      debugPrint('❌ خطأ في المزامنة: $e');
      Get.snackbar(
        'خطأ في المزامنة',
        'فشل في تحديث أحداث التقويم: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
        duration: const Duration(seconds: 3),
        icon: const Icon(Icons.error, color: AppColors.white),
      );
    }
  }

  /// عرض نافذة البحث
  void _showSearchDialog() {
    Get.snackbar(
      'البحث',
      'سيتم إضافة وظيفة البحث قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// تصدير التقويم
  void _exportCalendar() {
    Get.snackbar(
      'تصدير التقويم',
      'سيتم إضافة وظيفة التصدير قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// طباعة التقويم
  void _printCalendar() {
    Get.snackbar(
      'طباعة التقويم',
      'سيتم إضافة وظيفة الطباعة قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// عرض إعدادات التقويم
  void _showCalendarSettings() {
    Get.snackbar(
      'إعدادات التقويم',
      'سيتم إضافة إعدادات التقويم قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// عرض إحصائيات التقويم
  void _showCalendarStatistics() {
    Get.snackbar(
      'إحصائيات التقويم',
      'سيتم إضافة إحصائيات التقويم قريباً',
      snackPosition: SnackPosition.BOTTOM,
    );
  }

  /// بناء قائمة الأحداث المحسنة
  Widget _buildEventsList(List<CalendarEvent> events) {
    if (events.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.event_busy,
              size: 64,
              color: AppColors.textSecondary.withAlpha(128),
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد أحداث لهذا اليوم',
              style: AppStyles.bodyLarge.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              DateFormat('dd MMMM yyyy', 'ar').format(_selectedDay),
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
            const SizedBox(height: 16),
            if (_permissionService.canCreateCalendarEvents())
              ElevatedButton.icon(
                onPressed: () => _showAddEventDialog(),
                icon: const Icon(Icons.add),
                label: const Text('إضافة حدث جديد'),
                style: AppStyles.primaryButtonStyle,
              ),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: events.length,
      padding: const EdgeInsets.all(8),
      itemBuilder: (context, index) {
        final event = events[index];
        final eventColor = _parseColor(event.color) ?? _getEventTypeColor(event.eventType);
        final isTask = event.eventType == CalendarEventType.task || event.taskId != null;

        return Card(
          elevation: 3,
          margin: const EdgeInsets.symmetric(vertical: 4),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          child: InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () => _handleEventTap(event),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                border: Border(
                  right: BorderSide(
                    color: eventColor,
                    width: 4,
                  ),
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان الحدث ونوعه
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(6),
                          decoration: BoxDecoration(
                            color: eventColor.withAlpha(51),
                            borderRadius: BorderRadius.circular(6),
                          ),
                          child: Icon(
                            isTask ? Icons.task_alt : _getEventTypeIcon(event.eventType),
                            color: eventColor,
                            size: 16,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                isTask ? '📋 ${event.title}' : event.title,
                                style: AppStyles.bodyLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (isTask && event.task != null)
                                Text(
                                  'حالة: ${_getTaskStatusText(event.task!.status)} • أولوية: ${_getTaskPriorityText(event.task!.priority)}',
                                  style: AppStyles.bodySmall.copyWith(
                                    color: AppColors.textSecondary,
                                  ),
                                ),
                            ],
                          ),
                        ),
                        // أزرار الإجراءات
                        if (_permissionService.canEditCalendarEvents())
                          IconButton(
                            icon: const Icon(Icons.edit, size: 18),
                            onPressed: () {
                              Get.to(() => CalendarEventForm(
                                event: event,
                                onEventUpdated: (updatedEvent) {
                                  _loadEvents();
                                },
                              ));
                            },
                            tooltip: 'تعديل الحدث',
                          ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // وقت الحدث
                    Row(
                      children: [
                        Icon(
                          Icons.access_time,
                          size: 16,
                          color: AppColors.textSecondary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${_formatTime(event.startTime)} - ${_formatTime(event.endTime)}',
                          style: AppStyles.bodyMedium.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                        const SizedBox(width: 16),
                        if (event.isReminderEnabled)
                          Row(
                            children: [
                              Icon(
                                Icons.notifications_active,
                                size: 16,
                                color: AppColors.warning,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                'تنبيه',
                                style: AppStyles.bodySmall.copyWith(
                                  color: AppColors.warning,
                                ),
                              ),
                            ],
                          ),
                      ],
                    ),

                    // وصف الحدث (إذا وجد)
                    if (event.description != null && event.description!.isNotEmpty) ...[
                      const SizedBox(height: 8),
                      Text(
                        event.description!,
                        style: AppStyles.bodyMedium,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],

                    // المهمة المرتبطة (إذا وجدت)
                    if (event.taskId != null) ...[
                      const SizedBox(height: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.primary.withAlpha(51),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.task,
                              size: 14,
                              color: AppColors.primary,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'مرتبط بمهمة',
                              style: AppStyles.bodySmall.copyWith(
                                color: AppColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// تحليل اللون من النص
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  /// الحصول على لون نوع الحدث
  Color _getEventTypeColor(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return AppColors.info;
      case CalendarEventType.reminder:
        return AppColors.warning;
      case CalendarEventType.vacation:
        return AppColors.success;
      case CalendarEventType.other:
        return AppColors.info;
      default:
        return AppColors.primary;
    }
  }

  /// الحصول على أيقونة نوع الحدث
  IconData _getEventTypeIcon(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return Icons.task;
      case CalendarEventType.meeting:
        return Icons.meeting_room;
      case CalendarEventType.reminder:
        return Icons.notifications;
      case CalendarEventType.vacation:
        return Icons.beach_access;
      case CalendarEventType.other:
        return Icons.event;
      default:
        return Icons.event;
    }
  }

  /// تنسيق الوقت
  String _formatTime(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return DateFormat('HH:mm').format(dateTime);
  }

  /// الحصول على التواريخ المحظورة (أيام العطل)
  List<DateTime> _getBlackoutDates() {
    final List<DateTime> blackoutDates = [];
    final now = DateTime.now();

    // إضافة أيام العطل الثابتة (يمكن تخصيصها حسب البلد)
    // عيد رأس السنة
    blackoutDates.add(DateTime(now.year, 1, 1));

    // عيد العمال
    blackoutDates.add(DateTime(now.year, 5, 1));

    // يوم الاستقلال (مثال)
    blackoutDates.add(DateTime(now.year, 7, 4));

    // عيد الميلاد
    blackoutDates.add(DateTime(now.year, 12, 25));

    // يمكن إضافة المزيد من أيام العطل هنا
    // أو تحميلها من قاعدة البيانات أو API

    return blackoutDates;
  }






}