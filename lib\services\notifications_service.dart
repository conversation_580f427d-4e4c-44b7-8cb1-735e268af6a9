import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';

class NotificationsService {
  static final FlutterLocalNotificationsPlugin _notificationsPlugin = FlutterLocalNotificationsPlugin();
  static bool _initialized = false;
  static int _notificationId = 0;

  static Future<void> initialize(BuildContext context) async {
    if (_initialized) return;

    try {
      // إعدادات التهيئة لكل منصة
      const AndroidInitializationSettings initializationSettingsAndroid =
          AndroidInitializationSettings('@mipmap/ic_launcher');

      const DarwinInitializationSettings initializationSettingsIOS =
          DarwinInitializationSettings(
        requestSoundPermission: true,
        requestBadgePermission: true,
        requestAlertPermission: true,
      );

      const InitializationSettings initializationSettings = InitializationSettings(
        android: initializationSettingsAndroid,
        iOS: initializationSettingsIOS,
      );

      await _notificationsPlugin.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: (NotificationResponse response) async {
          // يمكنك هنا توجيه المستخدم إلى شاشة معينة عند الضغط على الإشعار
          debugPrint('تم الضغط على الإشعار: ${response.payload}');
        },
      );

      _initialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
      // تعيين _initialized = true حتى لو فشلت التهيئة لتجنب محاولات متكررة
      _initialized = true;
    }
  }

  static Future<void> showSimpleNotification({
    required String title,
    required String body,
    String? payload,
    String? type,
  }) async {
    if (!_initialized) {
      debugPrint('⚠️ خدمة الإشعارات غير مهيئة، سيتم تجاهل الإشعار');
      return;
    }

    try {
      // تحديد قناة الإشعار حسب النوع
      String channelId = 'default_channel_id';
      String channelName = 'الإشعارات';
      Importance importance = Importance.max;

      if (type != null) {
        switch (type) {
          case 'task_overdue':
          case 'reminder_6_hours':
            channelId = 'urgent_channel_id';
            channelName = 'إشعارات عاجلة';
            importance = Importance.max;
            break;
          case 'task_message_received':
          case 'comment_added':
            channelId = 'messages_channel_id';
            channelName = 'الرسائل والتعليقات';
            importance = Importance.high;
            break;
          default:
            channelId = 'general_channel_id';
            channelName = 'إشعارات عامة';
            importance = Importance.defaultImportance;
        }
      }

      final NotificationDetails notificationDetails = NotificationDetails(
        android: AndroidNotificationDetails(
          channelId,
          channelName,
          channelDescription: 'إشعارات النظام',
          importance: importance,
          priority: Priority.high,
          playSound: true,
        ),
        iOS: DarwinNotificationDetails(
          presentSound: true,
        ),
      );

      // استخدام ID فريد لكل إشعار
      await _notificationsPlugin.show(
        ++_notificationId,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint('✅ تم إرسال الإشعار: $title (ID: $_notificationId)');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار: $e');
      // لا نرمي الخطأ لتجنب توقف التطبيق
    }
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _initialized;

  /// إعادة تعيين حالة التهيئة (للاختبار)
  static void reset() {
    _initialized = false;
  }
}
