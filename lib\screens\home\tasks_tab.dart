import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart' as foundation;
import 'package:flutter_application_2/core/function.dart';
import 'package:get/get.dart';
import 'package:percent_indicator/linear_percent_indicator.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../constants/app_bar_styles.dart';

import '../../controllers/auth_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/user_controller.dart';
import '../../controllers/subtasks_controller.dart';
import '../../enums/task_enums.dart';
import '../../models/task_status_enum.dart' as task_enums;
import '../../models/task_models.dart';
import '../../models/user_model.dart';
import '../../utils/date_formatter.dart';
import '../../utils/database_repair_dialog.dart';
import '../../utils/task_detail_helpers.dart';
import '../widgets/simple_drag_drop_board.dart';
import '../widgets/draggable_divider.dart';
import '../tasks/task_detail_screen.dart';
// Import create_task_screen.dart with prefix to avoid ambiguous import of SizedBox
import '../tasks/create_task_screen.dart' as create_task_screen;
import '../../routes/app_routes.dart';
import '../../services/unified_signalr_service.dart'; // Import SignalRService
import '../../services/unified_permission_service.dart';
import '../../test_workload_report.dart'; // Import TestWorkloadReport

class TasksTab extends StatefulWidget {
  const TasksTab({super.key});

  @override
  State<TasksTab> createState() => _TasksTabState();
}

class _TasksTabState extends State<TasksTab>
    with SingleTickerProviderStateMixin, RouteAware {

  // الصلاحيات
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  // مراقب المسارات للتتبع عند العودة إلى الصفحة
  late RouteObserver<PageRoute> _routeObserver;
  late TabController _tabController;
  String? _filterStatus;
  String? _filterPriority;
  bool _isGroupedByStatus = true; // تجميع حسب الحالة
  bool _isListView = false; // عرض القائمة
  bool _isDragDropView = false; // عرض لوحة السحب والإفلات
  bool _showSidePanel = false; // عرض تفاصيل المهمة
  int? _selectedTaskId; // معرف المهمة المحددة

  // متغيرات للتحكم في حجم الأقسام
  double _mainPanelFlex = 2;
  double _sidePanelFlex = 1;
  final double _minPanelFlex = 1;
  final double _maxPanelFlex = 5;

  // متغيرات البحث
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  bool _isSearching = false;

  // متغيرات التصفية
  int? _filterAssignee;
  bool _filterHasSubtasks = false;
  double? _filterMinProgress;
  double? _filterMaxProgress;
  DateTime? _filterDueDateFrom;
  DateTime? _filterDueDateTo;
  DateTime? _filterCreationDateFrom;
  DateTime? _filterCreationDateTo;
  int? _filterTaskType;
  List<String> _filterTags =
      []; // سنحتفظ بهذا المتغير لكن لن نستخدمه في واجهة المستخدم

  // متغيرات الترتيب
  String _sortBy = 'dueDate'; // القيمة الافتراضية: تاريخ الاستحقاق
  String _sortDirection = 'asc'; // القيمة الافتراضية: تصاعدي



  @override
  void initState() {
    super.initState();
    // تهيئة TabController
    _tabController = TabController(length: 3, vsync: this);

    // Get UnifiedSignalRService instance
    final signalRService = Get.find<UnifiedSignalRService>();

    // Subscribe to SignalR events - محسن للأداء
    signalRService.chatHubConnection?.on("TaskUpdated", (arguments) {
      debugPrint("SignalR: Task Updated received: $arguments");
      // 🚀 التحسين: تحديث ذكي بدلاً من إعادة تحميل كامل
      _handleTaskUpdate(arguments);
    });

    signalRService.hubConnection?.on("TaskStatusUpdated", (arguments) {
       debugPrint("SignalR: Task Status Updated received: $arguments");
       // 🚀 التحسين: تحديث ذكي بدلاً من إعادة تحميل كامل
       _handleTaskStatusUpdate(arguments);
    });

    // Add more handlers for other events if needed (e.g., TaskAssignedToYou)
    // signalRService.hubConnection?.on("TaskAssignedToYou", (arguments) {
    //    debugPrint("SignalR: Task Assigned to You received: $arguments");
    //    _loadTasks(); // Or show a specific notification
    // });


    // الحصول على مراقب المسارات من GetX
    _routeObserver = Get.find<RouteObserver<PageRoute>>();

    // استخدام ميكانيكية addPostFrameCallback لتأخير تحميل المهام حتى اكتمال بناء الواجهة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _loadTasks(); // التحميل الأولي - استخدام cache إذا متوفر
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تسجيل هذه الصفحة مع مراقب المسارات
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // إلغاء تسجيل هذه الصفحة من مراقب المسارات
    _routeObserver.unsubscribe(this);
    _tabController.dispose();
    _searchController.dispose();

    // Unsubscribe from SignalR events
    final signalRService = Get.find<UnifiedSignalRService>();
    signalRService.hubConnection?.off("TaskUpdated");
    signalRService.hubConnection?.off("TaskStatusUpdated");
    // Unsubscribe from other events if added
    // signalRService.hubConnection?.off("TaskAssignedToYou");

    super.dispose();
  }

  /// يتم استدعاء هذه الدالة عند العودة إلى هذه الصفحة
  @override
  void didPopNext() {
    debugPrint('تم العودة إلى صفحة المهام');
    // إعادة تحميل المهام عند العودة إلى الصفحة - تحديث خفيف
    _loadTasks(forceRefresh: false);
  }

  /// بناء معاملات الاستعلام للتصفية
  Map<String, dynamic> _buildQueryParams() {
    final params = <String, dynamic>{};
    if (_filterStatus != null) params['status'] = _filterStatus;
    if (_filterPriority != null) params['priority'] = _filterPriority;
    if (_filterAssignee != null) params['assigneeId'] = _filterAssignee;
    if (_filterTaskType != null) params['taskTypeId'] = _filterTaskType;
    if (_filterDueDateFrom != null) params['dueDateFrom'] = _filterDueDateFrom!.millisecondsSinceEpoch ~/ 1000;
    if (_filterDueDateTo != null) params['dueDateTo'] = _filterDueDateTo!.millisecondsSinceEpoch ~/ 1000;
    if (_filterCreationDateFrom != null) params['createdFrom'] = _filterCreationDateFrom!.millisecondsSinceEpoch ~/ 1000;
    if (_filterCreationDateTo != null) params['createdTo'] = _filterCreationDateTo!.millisecondsSinceEpoch ~/ 1000;
    if (_filterMinProgress != null) params['minProgress'] = _filterMinProgress;
    if (_filterMaxProgress != null) params['maxProgress'] = _filterMaxProgress;
    if (_filterHasSubtasks) params['hasSubtasks'] = _filterHasSubtasks;

    // إضافة معاملات الترتيب
    params['sortBy'] = _sortBy;
    params['sortDirection'] = _sortDirection;

    return params;
  }

  /// معالجة تحديث مهمة محددة عبر SignalR - محسن للأداء
  void _handleTaskUpdate(List<dynamic>? arguments) {
    try {
      if (arguments != null && arguments.isNotEmpty) {
        // محاولة تحديث المهمة المحددة فقط
        final taskData = arguments[0];
        if (taskData is Map<String, dynamic> && taskData.containsKey('id')) {
          final taskId = taskData['id'];
          debugPrint('🔄 تحديث المهمة المحددة: $taskId');

          // تحديث المهمة في الذاكرة إذا أمكن
          // final taskController = Get.find<TaskController>(); // غير مستخدم
          // يمكن إضافة منطق تحديث المهمة المحددة هنا

          // إعادة تحميل خفيف (بدون forceRefresh)
          _loadTasks(forceRefresh: false);
        } else {
          // إذا لم نتمكن من تحديد المهمة، إعادة تحميل كامل
          _loadTasks(forceRefresh: true);
        }
      } else {
        // إعادة تحميل كامل كـ fallback
        _loadTasks(forceRefresh: true);
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة تحديث المهمة: $e');
      // إعادة تحميل كامل في حالة الخطأ
      _loadTasks(forceRefresh: true);
    }
  }

  /// معالجة تحديث حالة مهمة عبر SignalR - محسن للأداء
  void _handleTaskStatusUpdate(List<dynamic>? arguments) {
    try {
      if (arguments != null && arguments.isNotEmpty) {
        final statusData = arguments[0];
        if (statusData is Map<String, dynamic> && statusData.containsKey('taskId')) {
          final taskId = statusData['taskId'];
          debugPrint('🔄 تحديث حالة المهمة: $taskId');

          // تحديث خفيف للحالة
          _loadTasks(forceRefresh: false);
        } else {
          _loadTasks(forceRefresh: true);
        }
      } else {
        _loadTasks(forceRefresh: true);
      }
    } catch (e) {
      debugPrint('❌ خطأ في معالجة تحديث حالة المهمة: $e');
      _loadTasks(forceRefresh: true);
    }
  }

  /// تحميل المهام مع استراتيجية تخزين مؤقت ذكية - محسن
  Future<void> _loadTasks({bool forceRefresh = false}) async {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    debugPrint('🔄 بدء تحميل المهام - forceRefresh: $forceRefresh');
    debugPrint('👤 المستخدم الحالي: ${authController.currentUser.value?.name}');

    // إعادة تعيين الفلاتر لتجنب اختفاء المهام
    setState(() {
      // إعادة تعيين الفلاتر فقط إذا كانت المهام تختفي
      if (taskController.allTasks.isEmpty &&
          (_filterStatus != null ||
              _filterPriority != null ||
              _filterAssignee != null)) {
        debugPrint('🔄 إعادة تعيين الفلاتر لأن المهام فارغة');
        _filterStatus = null;
        _filterPriority = null;
        _filterAssignee = null;
        _filterMinProgress = null;
        _filterMaxProgress = null;
        _filterDueDateFrom = null;
        _filterDueDateTo = null;
        _filterCreationDateFrom = null;
        _filterCreationDateTo = null;
        _filterTaskType = null;
        _filterTags = [];
      }
    });

    if (authController.currentUser.value != null) {
      try {
        // 🚀 التحسين: استخدام التخزين المؤقت بذكاء
        // forceRefresh = false للتحميل العادي، true فقط عند الحاجة الفعلية
        await taskController.loadTasksByUserPermissions(
          authController.currentUser.value!.id,
          forceRefresh: forceRefresh,
        );

        debugPrint('📊 عدد المهام المحملة: ${taskController.allTasks.length}');
        debugPrint('❌ رسالة الخطأ: ${taskController.error}');

        // طباعة تفاصيل المهام المحملة
        if (taskController.allTasks.isNotEmpty) {
          debugPrint('📋 تفاصيل المهام المحملة:');
          for (int i = 0; i < taskController.allTasks.length && i < 5; i++) {
            final task = taskController.allTasks[i];
            debugPrint('   ${i + 1}. "${task.title}" - حالة: ${task.status} - أولوية: ${task.priority}');
          }
          if (taskController.allTasks.length > 5) {
            debugPrint('   ... و ${taskController.allTasks.length - 5} مهمة أخرى');
          }
        }

        // التحقق من وجود مهام بعد التحميل
        if (taskController.allTasks.isEmpty &&
            taskController.error.isEmpty) {
          debugPrint('⚠️ لا توجد مهام ولا يوجد خطأ');
          // لا توجد مهام ولا يوجد خطأ - هذا طبيعي
          Get.snackbar(
            'معلومات',
            'لا توجد مهام متاحة حالياً. يمكنك إنشاء مهام جديدة.',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.info.withAlpha(51),
            colorText: AppColors.info,
            duration: const Duration(seconds: 3),
          );
        } else if (taskController.error.isNotEmpty) {
          debugPrint('❌ حدث خطأ أثناء التحميل: ${taskController.error}');
          // حدث خطأ أثناء التحميل
          Get.snackbar(
            'خطأ في تحميل المهام',
            taskController.error,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error.withAlpha(51),
            colorText: AppColors.error,
          );
        } else {
          debugPrint('✅ تم تحميل المهام بنجاح');
          // إضافة رسالة توضيحية للمستخدم حول المهام المعروضة
          String message = 'تم تحميل ${taskController.allTasks.length} مهمة بناءً على صلاحياتك';

          // عرض رسالة توضيحية للمستخدم
          Get.snackbar(
            'معلومات',
            message,
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.success.withAlpha(51),
            colorText: AppColors.success,
            duration: const Duration(seconds: 3),
          );
        }
      } catch (e) {
        debugPrint('💥 خطأ غير متوقع: $e');
        // خطأ غير متوقع
        Get.snackbar(
          'خطأ',
          'حدث خطأ غير متوقع: $e',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.error.withAlpha(51),
          colorText: AppColors.error,
          duration: const Duration(seconds: 5),
          mainButton: TextButton(
            onPressed: () {
              // عرض مربع حوار إصلاح قاعدة البيانات
              if (e.toString().contains('SQL') ||
                  e.toString().contains('database')) {
                DatabaseRepairDialog.showDatabaseErrorDialog(context, e.toString());
              }
            },
            child: Text('إصلاح', style: AppStyles.labelSmall.copyWith(color: AppColors.surface)),
          ),
        );
      }
    } else {
      debugPrint('❌ لا يوجد مستخدم مسجل');
      // في حالة عدم وجود مستخدم مسجل، نعرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'يجب تسجيل الدخول لعرض المهام',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.surface,
      );
    }
  }

  /// تنفيذ البحث في المهام مع تطبيق الصلاحيات
  void _performSearch(String query) {
    if (!_permissionService.canAccessTasks()) {
      return;
    }

    final taskController = Get.find<TaskController>();

    if (query.isEmpty) {
      // إذا كان البحث فارغاً، إعادة تحميل المهام العادية
      _loadTasks(forceRefresh: false); // البحث الفارغ لا يحتاج forceRefresh
    } else {
      // تطبيق البحث باستخدام TaskController
      taskController.searchTasks(query);
    }
  }

  /// الحصول على المهام المراد عرضها (مع أو بدون بحث)
  List<Task> _getTasksToDisplay(TaskController taskController) {
    if (_isSearching && _searchQuery.isNotEmpty) {
      return taskController.filteredTasks;
    }
    return taskController.allTasks;
  }



  /// بناء عنوان صفحة المهام
  Widget _buildTasksTitle() {
    final permissionService = Get.find<UnifiedPermissionService>();

    // العنوان الرئيسي دائمًا "المهام"
    String subtitle = '';

    // إضافة عنوان فرعي حسب صلاحيات المستخدم
    if (permissionService.canViewAllTasks()) {
      subtitle = 'جميع المهام في النظام';
    } else if (permissionService.canAdminDepartments()) {
      subtitle = 'مهام القسم';
    } else {
      subtitle = 'المهام المخصصة لك';
    }

    // إذا لم يكن هناك عنوان فرعي، نعرض العنوان الرئيسي فقط
    if (subtitle.isEmpty) {
      return Text('المهام', style: AppStyles.titleMedium.copyWith(color: AppColors.appBarText));
    }

    // عرض العنوان الرئيسي والفرعي
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Text('المهام', style: AppStyles.titleMedium.copyWith(color: AppColors.appBarText)),
        Text(
          subtitle,
          style: AppStyles.bodySmall.copyWith(
            color: AppColors.appBarText,
            fontWeight: FontWeight.normal,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    final taskController = Get.find<TaskController>();

    return Scaffold(
      appBar: AppBar(
        title: _buildTasksTitle(),
        actions: [
          // Search button
          if (_permissionService.canAccessTasks())
            AppBarStyles.appBarIconButton(
              icon: _isSearching ? Icons.search_off : Icons.search,
              tooltip: _isSearching ? 'إلغاء البحث' : 'البحث في المهام',
              onPressed: () {
                if (_isSearching) {
                  // إلغاء البحث
                  _searchController.clear();
                  setState(() {
                    _searchQuery = '';
                    _isSearching = false;
                  });
                  _loadTasks();
                } else {
                  // تفعيل وضع البحث
                  setState(() {
                    _isSearching = true;
                  });
                }
              },
            ),

          
          // View toggle button - toggle list view within the same screen
          if (_permissionService.canAccessTasks())
            IconButton(
              icon: Icon(
                _isListView ? Icons.view_module : Icons.view_list,
                color: AppColors.appBarIcon,
              ),
              tooltip: _isListView ? 'عرض المجموعات' : 'عرض القائمة',
              onPressed: () {
                setState(() {
                  _isListView = !_isListView;
                  // عند تغيير طريقة العرض، نلغي العروض الأخرى
                  _isDragDropView = false;
                });
              },
            ),
          // Drag & Drop Board button
          if (_permissionService.canViewTaskBoard())
            IconButton(
              icon: Icon(
                _isDragDropView ? Icons.view_module : Icons.drag_indicator,
                color: AppColors.appBarIcon,
              ),
              tooltip:
                  _isDragDropView ? 'العودة للعرض العادي' : 'لوحة السحب والإفلات',
              onPressed: () {
                setState(() {
                  _isDragDropView = !_isDragDropView;
                  // عند تفعيل عرض السحب والإفلات، نلغي العروض الأخرى
                  if (_isDragDropView) {
                    _isListView = false;
                  }
                });
              },
            ),
          // Filter button with badge for active filters
          if (_permissionService.canFilterTasks())
            Stack(
              alignment: Alignment.center,
              children: [
                IconButton(
                  icon: Icon(Icons.filter_alt_outlined, color: AppColors.appBarIcon),
                  tooltip: 'تصفية المهام',
                  onPressed: _showFilterDialog,
                ),
              if (_hasActiveFilters())
                Positioned(
                  top: 8,
                  right: 8,
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: AppColors.error,
                      borderRadius: BorderRadius.circular(6),
                    ),
                    constraints: const BoxConstraints(
                      minWidth: 12,
                      minHeight: 12,
                    ),
                    child: Text(
                      _getActiveFiltersCount().toString(),
                      style: AppStyles.labelSmall.copyWith(
                        color: AppColors.surface,
                        fontSize: 8,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ),
                ),
            ],
          ),
          // Clear filters button (only visible when filters are active)
          if (_hasActiveFilters() && _permissionService.canFilterTasks())
            IconButton(
              icon: Icon(Icons.filter_alt_off_outlined, color: AppColors.error),
              tooltip: 'إلغاء جميع المرشحات',
              onPressed: _clearAllFilters,
            ),
          // Sort button
          // IconButton(
          //   icon: const Icon(Icons.sort),
          //   tooltip: 'ترتيب المهام',
          //   onPressed: _showSortDialog,
          // ),
          // Refresh button
          
          if (_permissionService.canRefreshTasks())
            IconButton(
              icon: Icon(Icons.refresh, color: AppColors.appBarIcon),
              tooltip: 'تحديث',
              onPressed: () => _loadTasks(forceRefresh: true), // زر التحديث يجب أن يفرض التحديث
            ),
          
          // Test workload report button (only in debug mode)
          if (foundation.kDebugMode)
            // زر تقرير عبء العمل
            if (_permissionService.canViewWorkloadReport())
              IconButton(
                icon: Icon(Icons.analytics_outlined, color: AppColors.appBarIcon),
                tooltip: 'اختبار تقرير عبء العمل',
                onPressed: () {
                  Get.to(() => const TestWorkloadReport());
                },
              ),
          
        ],
      ),
      // إضافة شريط معلومات لتوضيح إمكانية السحب والإفلات
      bottomNavigationBar: Container(
        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
        color: AppColors.card,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              textDirection: TextDirection.rtl,
              children: [
                Icon(Icons.drag_indicator,
                    color: AppColors.primary),
                const SizedBox(width: 2),
                Expanded(
                  child: Text(
                    'يمكنك سحب المهام وإفلاتها بين المجموعات لتغيير حالتها أو إلى شريط الأولويات لتغيير الأولوية',
                    style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.primary),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ),
              ],
            ),
            if (!_isGroupedByStatus || _isListView || _isDragDropView)
              Padding(
                padding: const EdgeInsets.only(top: 4),
                child: Text(
                  'لتفعيل السحب والإفلات: اضغط على ${_isGroupedByStatus ? "" : "أيقونة المجموعات"} ${!_isGroupedByStatus || !_isListView ? "" : "وأيقونة المجموعات"} ${!_isDragDropView ? "" : "وأيقونة العرض العادي"}',
                  style: AppStyles.labelMedium.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
          ],
        ),
      ),
      // إضافة شريط الأولويات في أعلى الشاشة عند تفعيل عرض المجموعات
      persistentFooterButtons: _isGroupedByStatus &&
              !_isListView &&
              !_isDragDropView
          ? [
              Container(
                padding:
                    const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
                decoration: BoxDecoration(
                  color: AppColors.card,
                  borderRadius: BorderRadius.circular(8),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.getShadowColor(0.1),
                      blurRadius: 5,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // const Text(
                    //   'اسحب المهام إلى الأولويات التالية لتغييرها:',
                    //   style: TextStyle(
                    //     fontWeight: FontWeight.bold,
                    //     fontSize: 14,
                    //   ),
                    // ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              'مناطق الإفلات',
                              style: AppStyles.labelMedium.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                _buildPriorityDropTarget(TaskPriority.low),
                                const SizedBox(width: 8),
                                _buildPriorityDropTarget(TaskPriority.medium),
                                const SizedBox(width: 8),
                                _buildPriorityDropTarget(TaskPriority.high),
                                const SizedBox(width: 8),
                                _buildPriorityDropTarget(TaskPriority.urgent),
                              ],
                            ),
                          ],
                        ),
                        Container(
                          height: 20,
                          width: 1,
                          color: AppColors.border,
                        ),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.center,
                          children: [
                            Text(
                              'اسحب هذه الأولويات إلى المهام',
                              style: AppStyles.labelMedium.copyWith(
                                color: AppColors.textPrimary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                _buildDraggablePriority(TaskPriority.low),
                                const SizedBox(width: 8),
                                _buildDraggablePriority(TaskPriority.medium),
                                const SizedBox(width: 8),
                                _buildDraggablePriority(TaskPriority.high),
                                const SizedBox(width: 8),
                                _buildDraggablePriority(TaskPriority.urgent),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ]
          : null,
      body: Row(
        children: [
          // Main content
          Expanded(
            flex: _mainPanelFlex.toInt(),
            child: _isDragDropView
                ? _buildDragDropBoard(_getTasksToDisplay(taskController))
                : Column(
                    children: [


                      // Tab bar
                      if (_permissionService.canFilterTasks())
                        TabBar(
                          controller: _tabController,
                          labelColor: AppColors.primary,
                          unselectedLabelColor: AppColors.textSecondary,
                          indicatorColor: AppColors.primary,
                          tabs: const [
                            Tab(text: 'جميع المهام'),
                            Tab(text: 'قيد التنفيذ'),
                            Tab(text: 'مكتملة'),
                          ],
                        ),

                      // شريط البحث المباشر
                      if (_isSearching && _permissionService.canAccessTasks())
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: AppColors.card,
                            border: Border(
                              bottom: BorderSide(
                                color: AppColors.border,
                              ),
                            ),
                          ),
                          child: TextField(
                            controller: _searchController,
                            style: AppStyles.bodyMedium.copyWith(
                              color: AppColors.textPrimary,
                            ),
                            decoration: AppStyles.inputDecoration(
                              labelText: 'البحث في المهام',
                              hintText: 'رقم المهمة، الاسم، الوصف، رقم الوارد...',
                              prefixIcon: Icon(Icons.search, color: AppColors.textSecondary),
                              suffixIcon: IconButton(
                                icon: Icon(Icons.clear, color: AppColors.textSecondary),
                                onPressed: () {
                                  _searchController.clear();
                                  setState(() {
                                    _searchQuery = '';
                                    _isSearching = false;
                                  });
                                  _loadTasks();
                                },
                              ),
                            ),
                            autofocus: true,
                            onChanged: (value) {
                              setState(() {
                                _searchQuery = value;
                              });
                              _performSearch(value);
                            },
                            onSubmitted: (value) {
                              setState(() {
                                _searchQuery = value;
                              });
                              _performSearch(value);
                            },
                          ),
                        ),

                      // Tab content
                      Expanded(
                        child: TabBarView(
                          controller: _tabController,
                          children: [
                            // All Tasks tab
                            _isListView
                                ? _buildTasksTable(_getTasksToDisplay(taskController))
                                : _isGroupedByStatus
                                    ? _buildGroupedTasksView(
                                        _getTasksToDisplay(taskController))
                                    : _buildTasksTable(_getTasksToDisplay(taskController)),

                            // In Progress tab
                            _isListView
                                ? _buildTasksTable(_getTasksToDisplay(taskController)
                                    .where((task) =>
                                        task.status == 'pending' ||
                                        task.status == 'in_progress' ||
                                        task.status == 'waiting_for_info')
                                    .toList())
                                : _isGroupedByStatus
                                    ? _buildGroupedTasksView(_getTasksToDisplay(taskController)
                                        .where((task) =>
                                            task.status == 'pending' ||
                                            task.status == 'in_progress' ||
                                            task.status == 'waiting_for_info')
                                        .toList())
                                    : _buildTasksTable(_getTasksToDisplay(taskController)
                                        .where((task) =>
                                            task.status == 'pending' ||
                                            task.status == 'in_progress' ||
                                            task.status == 'waiting_for_info')
                                        .toList()),

                            // Completed tab
                            _isListView
                                ? _buildTasksTable(_getTasksToDisplay(taskController)
                                    .where((task) =>
                                        task.status == 'completed')
                                    .toList())
                                : _isGroupedByStatus
                                    ? _buildGroupedTasksView(_getTasksToDisplay(taskController)
                                        .where((task) =>
                                            task.status == 'completed')
                                        .toList())
                                    : _buildTasksTable(_getTasksToDisplay(taskController)
                                        .where((task) =>
                                            task.status == 'completed')
                                        .toList()),
                          ],
                        ),
                      ),
                    ],
                  ),
          ),

          // فاصل قابل للسحب (يظهر فقط عند عرض اللوحة الجانبية)
          if (_showSidePanel && _selectedTaskId != null)
            DraggableDivider(
              direction: Axis.vertical,
              thickness: 8.0,
              color: AppColors.border,
              activeColor: AppColors.primary.withAlpha(51),
              onDrag: (delta) {
                // تحديث حجم الأقسام بناءً على حركة السحب
                setState(() {
                  // تحويل دلتا إلى تغيير في نسبة الـ flex
                  // قيمة موجبة تعني زيادة اليسار وتقليل اليمين
                  final flexDelta =
                      delta / 50; // معامل تحويل لجعل الحركة أكثر سلاسة

                  // التحقق من الحدود
                  if (_mainPanelFlex + flexDelta >= _minPanelFlex &&
                      _sidePanelFlex - flexDelta >= _minPanelFlex &&
                      _mainPanelFlex + flexDelta <= _maxPanelFlex &&
                      _sidePanelFlex - flexDelta <= _maxPanelFlex) {
                    _mainPanelFlex += flexDelta;
                    _sidePanelFlex -= flexDelta;
                  }
                });
              },
            ),

          // Side panel for task details
          if (_showSidePanel && _selectedTaskId != null)
            Expanded(
              flex: _sidePanelFlex.toInt(),
              child: Container(
                decoration: BoxDecoration(
                  color: AppColors.card,
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.getShadowColor(0.1),
                      blurRadius: 5,
                      offset: const Offset(-2, 0),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    // Side panel header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: AppColors.card,
                        border: Border(
                          bottom: BorderSide(
                              color: AppColors.border),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              // أيقونة المهمة
                              Container(
                                padding: const EdgeInsets.all(8),
                                decoration: BoxDecoration(
                                  color: AppColors.primary.withAlpha(51),
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: const Icon(
                                  Icons.task_alt,
                                  color: AppColors.primary,
                                ),
                              ),
                              const SizedBox(width: 12),
                              // عنوان الشريط الجانبي
                              Text(
                                'تفاصيل المهمة',
                                style: AppStyles.titleLarge.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                ),
                              ),
                            ],
                          ),
                          // أزرار الإجراءات
                          Row(
                            children: [
                              // زر فتح في نافذة جديدة
                              if (_permissionService.canViewTaskDetails())
                                IconButton(
                                  icon: Icon(Icons.open_in_new, color: AppColors.textPrimary),
                                  tooltip: 'فتح في نافذة جديدة',
                                  onPressed: () {
                                    Get.toNamed(AppRoutes.taskDetail,
                                        arguments: {'taskId': _selectedTaskId!});
                                  },
                                ),
                              // زر الإغلاق
                              IconButton(
                                icon: Icon(Icons.close, color: AppColors.error),
                                tooltip: 'إغلاق',
                                onPressed: () {
                                  setState(() {
                                    _showSidePanel = false;
                                    _selectedTaskId = null;
                                  });
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    // Task details content
                    Expanded(
                      child: TaskDetailScreen(taskId: _selectedTaskId!.toString()),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
      floatingActionButton: _permissionService.canCreateTask()
          ? FloatingActionButton(
              heroTag: 'tasks_tab_fab',
              backgroundColor: AppColors.primary,
              foregroundColor: AppColors.surface,
              onPressed: () {
                // Navigate to create task screen
                Get.toNamed(AppRoutes.createTask);
              },
              child: Icon(Icons.add, color: AppColors.surface),
            )
          : null,
    );
  }

  // String getStatusText(String status) {
  //   // تطبيع الحالة للتعامل مع جميع الاحتمالات
  //   final normalizedStatus = status.toLowerCase().trim();

  //   debugPrint('🔍 getStatusText: الحالة الأصلية: "$status" -> المطبعة: "$normalizedStatus"');

  //   switch (normalizedStatus) {
  //     // حالات الانتظار
  //     case 'pending':
  //     case 'new':
  //     case 'news':
  //     case 'جديد':
  //     case 'جديدة':
  //       return 'قيد الانتظار';

  //     // حالات التنفيذ
  //     case 'inprogress':
  //     case 'in_progress':
  //     case 'in-progress':
  //     case 'قيد التنفيذ':
  //       return 'قيد التنفيذ';

  //     // حالات انتظار المعلومات
  //     case 'waitingforinfo':
  //     case 'waiting_for_info':
  //     case 'waiting-for-info':
  //     case 'في انتظار معلومات':
  //       return 'في انتظار معلومات';

  //     // حالات الاكتمال
  //     case 'completed':
  //     case 'done':
  //     case 'مكتملة':
  //       return 'مكتملة';

  //     // حالات الإلغاء
  //     case 'cancelled':
  //     case 'canceled':
  //     case 'ملغاة':
  //       return 'ملغاة';

  //     default:
  //       debugPrint('⚠️ حالة غير معروفة: "$status"');
  //       return 'غير محدد ($status)';
  //   }
  // }

  // String getPriorityText(dynamic priority) {
  //   if (priority is TaskPriority) {
  //     switch (priority) {
  //       case TaskPriority.low:
  //         return 'منخفضة';
  //       case TaskPriority.medium:
  //         return 'متوسطة';
  //       case TaskPriority.high:
  //         return 'عالية';
  //       case TaskPriority.urgent:
  //         return 'عاجلة';
  //     }
  //   } else if (priority is int) {
  //     final taskPriority = TaskPriority.fromLevel(priority);
  //     return getPriorityText(taskPriority);
  //   } else if (priority is String) {
  //     // حاول التحويل باستخدام fromString (يدعم stringValue واسم enum)
  //     final taskPriority = TaskPriority.fromString(priority);
  //     return getPriorityText(taskPriority);
  //   }
  //   return 'غير محدد';
  // }

  // /// الحصول على أيقونة الأولوية
  // IconData _getPriorityIcon(dynamic priority) {
  //   if (priority is TaskPriority) {
  //     switch (priority) {
  //       case TaskPriority.low:
  //         return Icons.arrow_downward;
  //       case TaskPriority.medium:
  //         return Icons.remove;
  //       case TaskPriority.high:
  //         return Icons.arrow_upward;
  //       case TaskPriority.urgent:
  //         return Icons.priority_high;
  //     }
  //   } else if (priority is int) {
  //     final taskPriority = TaskPriority.fromLevel(priority);
  //     return _getPriorityIcon(taskPriority);
  //   }
  //   return Icons.help;
  // }

  /// عرض مربع حوار التصفية
  void _showFilterDialog() {
    showDialog(
      context: context,
      builder: (context) {
        // متغيرات مؤقتة للتصفية
        String? tempStatus = _filterStatus;
        String? tempPriority = _filterPriority;
        int? tempAssignee = _filterAssignee;
        bool tempHasSubtasks = _filterHasSubtasks;
        double? tempMinProgress = _filterMinProgress;
        double? tempMaxProgress = _filterMaxProgress;
        DateTime? tempDueDateFrom = _filterDueDateFrom;
        DateTime? tempDueDateTo = _filterDueDateTo;
        DateTime? tempCreationDateFrom = _filterCreationDateFrom;
        DateTime? tempCreationDateTo = _filterCreationDateTo;
        int? tempTaskType = _filterTaskType;
        List<String> tempTags = List.from(_filterTags);

        // متغيرات مؤقتة للترتيب
        String tempSortBy = _sortBy;
        String tempSortDirection = _sortDirection;

        // تم حذف قائمة الوسوم بناءً على طلب المستخدم

        // قائمة أنواع المهام (يمكن استبدالها بقائمة ديناميكية من قاعدة البيانات)
        final userController = Get.find<UserController>();

        return StatefulBuilder(
          builder: (context, setDialogState) {
            return Dialog(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                width: MediaQuery.of(context).size.width * 0.8,
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // عنوان مربع الحوار
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'تصفية وترتيب المهام',
                          style: AppStyles.titleLarge.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.textPrimary,
                          ),
                        ),
                        // زر إعادة تعيين الفلاتر
                        TextButton.icon(
                          icon: const Icon(Icons.refresh),
                          label: Text('إعادة تعيين', style: TextStyle(color: AppColors.textPrimary)),
                          onPressed: () {
                            setDialogState(() {
                              tempStatus = null;
                              tempPriority = null;
                              tempAssignee = null;
                              tempHasSubtasks = false;
                              tempMinProgress = null;
                              tempMaxProgress = null;
                              tempDueDateFrom = null;
                              tempDueDateTo = null;
                              tempCreationDateFrom = null;
                              tempCreationDateTo = null;
                              tempTaskType = null;
                              tempTags = [];
                              tempSortBy = 'dueDate';
                              tempSortDirection = 'asc';
                            });
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),

                    // استخدام Expanded مع SingleChildScrollView لتمكين التمرير
                    Expanded(
                      child: SingleChildScrollView(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // قسم التصفية
                            Text(
                              'خيارات التصفية',
                              style: AppStyles.titleMedium.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const Divider(),

                            // تصفية حسب الحالة
                            Text('الحالة',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Wrap(
                              spacing: 8,
                              children: [
                                FilterChip(
                                  label: Text('الكل', style: AppStyles.labelSmall.copyWith(
                                    color: tempStatus == null ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempStatus == null,
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempStatus = null;
                                      });
                                    }
                                  },
                                ),
                                ...TaskStatus.values.map((status) {
                                  return FilterChip(
                                    label: Text(getStatusText(status.name), style: AppStyles.labelSmall.copyWith(
                                      color: tempStatus == status.name ? AppColors.surface : AppColors.textPrimary,
                                    )),
                                    selected: tempStatus == status.name,
                                    selectedColor: AppColors.getTaskStatusColor(status.name),
                                    backgroundColor: AppColors.card,
                                    onSelected: (selected) {
                                      setDialogState(() {
                                        tempStatus = selected ? status.name : null;
                                      });
                                    },
                                  );
                                }),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب الأولوية
                            Text('الأولوية',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Wrap(
                              spacing: 8,
                              children: [
                                FilterChip(
                                  label: Text('الكل', style: AppStyles.labelSmall.copyWith(
                                    color: tempPriority == null ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempPriority == null,
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempPriority = null;
                                      });
                                    }
                                  },
                                ),
                                ...TaskPriority.values.map((priority) {
                                  return FilterChip(
                                    label: Text(getPriorityText(priority), style: AppStyles.labelSmall.copyWith(
                                      color: tempPriority == _getPriorityIdFromLevel(priority.level) ? AppColors.surface : AppColors.textPrimary,
                                    )),
                                    selected: tempPriority == _getPriorityIdFromLevel(priority.level),
                                    selectedColor: AppColors.getTaskPriorityColor(priority.name),
                                    backgroundColor: AppColors.card,
                                    onSelected: (selected) {
                                      setDialogState(() {
                                        tempPriority =
                                            selected ? _getPriorityIdFromLevel(priority.level) : null;
                                      });
                                    },
                                  );
                                }),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب المسؤول
                            Text('المسؤول',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            FutureBuilder<List<User>>(
                              future: userController
                                  .loadAllUsers()
                                  .then((_) => userController.users),
                              builder: (context, snapshot) {
                                if (snapshot.connectionState ==
                                    ConnectionState.waiting) {
                                  return const CircularProgressIndicator();
                                }

                                final users = snapshot.data ?? [];
                                return Wrap(
                                  spacing: 8,
                                  children: [
                                    FilterChip(
                                      label: Text('الكل', style: AppStyles.labelSmall.copyWith(
                                        color: tempAssignee == null ? AppColors.surface : AppColors.textPrimary,
                                      )),
                                      selected: tempAssignee == null,
                                      selectedColor: AppColors.primary,
                                      backgroundColor: AppColors.card,
                                      onSelected: (selected) {
                                        if (selected) {
                                          setDialogState(() {
                                            tempAssignee = null;
                                          });
                                        }
                                      },
                                    ),
                                    FilterChip(
                                      label: Text('مهامي', style: AppStyles.labelSmall.copyWith(
                                        color: tempAssignee == -1 ? AppColors.surface : AppColors.textPrimary,
                                      )),
                                      selected: tempAssignee == -1,
                                      selectedColor: AppColors.primary,
                                      backgroundColor: AppColors.card,
                                      onSelected: (selected) {
                                        setDialogState(() {
                                          tempAssignee = selected ? -1 : null;
                                        });
                                      },
                                    ),
                                    ...users.map((user) {
                                      return FilterChip(
                                        label: Text(user.name, style: AppStyles.labelSmall.copyWith(
                                          color: tempAssignee == user.id ? AppColors.surface : AppColors.textPrimary,
                                        )),
                                        selected: tempAssignee == user.id,
                                        selectedColor: AppColors.primary,
                                        backgroundColor: AppColors.card,
                                        onSelected: (selected) {
                                          setDialogState(() {
                                            tempAssignee =
                                                selected ? user.id : null;
                                          });
                                        },
                                      );
                                    }),
                                  ],
                                );
                              },
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب تاريخ الاستحقاق
                            Text('تاريخ الاستحقاق',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'من',
                                      prefixIcon: Icon(Icons.calendar_today, color: AppColors.textSecondary),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempDueDateFrom != null
                                          ? DateFormatter.formatDate(
                                              tempDueDateFrom!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate:
                                            tempDueDateFrom ?? DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempDueDateFrom = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'إلى',
                                      prefixIcon: Icon(Icons.calendar_today, color: AppColors.textSecondary),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempDueDateTo != null
                                          ? DateFormatter.formatDate(
                                              tempDueDateTo!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate:
                                            tempDueDateTo ?? DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempDueDateTo = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب تاريخ الإنشاء
                            Text('تاريخ الإنشاء',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'من',
                                      prefixIcon: Icon(Icons.calendar_today, color: AppColors.textSecondary),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempCreationDateFrom != null
                                          ? DateFormatter.formatDate(
                                              tempCreationDateFrom!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: tempCreationDateFrom ??
                                            DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempCreationDateFrom = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'إلى',
                                      prefixIcon: Icon(Icons.calendar_today, color: AppColors.textSecondary),
                                    ),
                                    readOnly: true,
                                    controller: TextEditingController(
                                      text: tempCreationDateTo != null
                                          ? DateFormatter.formatDate(
                                              tempCreationDateTo!)
                                          : '',
                                    ),
                                    onTap: () async {
                                      final date = await showDatePicker(
                                        context: context,
                                        initialDate: tempCreationDateTo ??
                                            DateTime.now(),
                                        firstDate: DateTime(2020),
                                        lastDate: DateTime(2030),
                                      );
                                      if (date != null) {
                                        setDialogState(() {
                                          tempCreationDateTo = date;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب نسبة التقدم
                            Text('نسبة التقدم',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Row(
                              children: [
                                Expanded(
                                  child: TextFormField(
                                    style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'من',
                                      prefixIcon: Icon(Icons.percent, color: AppColors.textSecondary),
                                    ),
                                    keyboardType: TextInputType.number,
                                    controller: TextEditingController(
                                      text: tempMinProgress?.toString() ?? '',
                                    ),
                                    onChanged: (value) {
                                      if (value.isNotEmpty) {
                                        final progress = double.tryParse(value);
                                        if (progress != null &&
                                            progress >= 0 &&
                                            progress <= 100) {
                                          setDialogState(() {
                                            tempMinProgress = progress / 100;
                                          });
                                        }
                                      } else {
                                        setDialogState(() {
                                          tempMinProgress = null;
                                        });
                                      }
                                    },
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Expanded(
                                  child: TextFormField(
                                    style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary),
                                    decoration: AppStyles.inputDecoration(
                                      labelText: 'إلى',
                                      prefixIcon: Icon(Icons.percent, color: AppColors.textSecondary),
                                    ),
                                    keyboardType: TextInputType.number,
                                    controller: TextEditingController(
                                      text: tempMaxProgress?.toString() ?? '',
                                    ),
                                    onChanged: (value) {
                                      if (value.isNotEmpty) {
                                        final progress = double.tryParse(value);
                                        if (progress != null &&
                                            progress >= 0 &&
                                            progress <= 100) {
                                          setDialogState(() {
                                            tempMaxProgress = progress / 100;
                                          });
                                        }
                                      } else {
                                        setDialogState(() {
                                          tempMaxProgress = null;
                                        });
                                      }
                                    },
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // تصفية حسب وجود مهام فرعية
                            Row(
                              children: [
                                Checkbox(
                                  value: tempHasSubtasks,
                                  activeColor: AppColors.primary,
                                  checkColor: AppColors.surface,
                                  onChanged: (value) {
                                    setDialogState(() {
                                      tempHasSubtasks = value ?? false;
                                    });
                                  },
                                ),
                                Text(
                                    'المهام التي تحتوي على مهام فرعية فقط',
                                    style: AppStyles.bodyMedium.copyWith(
                                      color: AppColors.textPrimary,
                                    ),
                                  ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            const SizedBox(height: 24),

                            // قسم الترتيب
                            Text(
                              'خيارات الترتيب',
                              style: AppStyles.titleMedium.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppColors.textPrimary,
                              ),
                            ),
                            const Divider(),

                            // ترتيب حسب
                            Text('ترتيب حسب',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Wrap(
                              spacing: 8,
                              children: [
                                ChoiceChip(
                                  label: Text('العنوان', style: AppStyles.labelSmall.copyWith(
                                    color: tempSortBy == 'title' ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempSortBy == 'title',
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'title';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: Text('تاريخ الاستحقاق', style: AppStyles.labelSmall.copyWith(
                                    color: tempSortBy == 'dueDate' ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempSortBy == 'dueDate',
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'dueDate';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: Text('تاريخ الإنشاء', style: AppStyles.labelSmall.copyWith(
                                    color: tempSortBy == 'createdAt' ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempSortBy == 'createdAt',
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'createdAt';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: Text('الأولوية', style: AppStyles.labelSmall.copyWith(
                                    color: tempSortBy == 'priority' ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempSortBy == 'priority',
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'priority';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: Text('الحالة', style: AppStyles.labelSmall.copyWith(
                                    color: tempSortBy == 'status' ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempSortBy == 'status',
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'status';
                                      });
                                    }
                                  },
                                ),
                                ChoiceChip(
                                  label: Text('نسبة التقدم', style: AppStyles.labelSmall.copyWith(
                                    color: tempSortBy == 'progress' ? AppColors.surface : AppColors.textPrimary,
                                  )),
                                  selected: tempSortBy == 'progress',
                                  selectedColor: AppColors.primary,
                                  backgroundColor: AppColors.card,
                                  onSelected: (selected) {
                                    if (selected) {
                                      setDialogState(() {
                                        tempSortBy = 'progress';
                                      });
                                    }
                                  },
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // اتجاه الترتيب
                            Text('اتجاه الترتيب',
                                style: AppStyles.labelMedium.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: AppColors.textPrimary,
                                )),
                            Row(
                              children: [
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: Text('تصاعدي', style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary)),
                                    value: 'asc',
                                    groupValue: tempSortDirection,
                                    activeColor: AppColors.primary,
                                    onChanged: (value) {
                                      setDialogState(() {
                                        tempSortDirection = value!;
                                      });
                                    },
                                  ),
                                ),
                                Expanded(
                                  child: RadioListTile<String>(
                                    title: Text('تنازلي', style: AppStyles.bodyMedium.copyWith(color: AppColors.textPrimary)),
                                    value: 'desc',
                                    groupValue: tempSortDirection,
                                    activeColor: AppColors.primary,
                                    onChanged: (value) {
                                      setDialogState(() {
                                        tempSortDirection = value!;
                                      });
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    // أزرار الإجراءات
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16.0, vertical: 8.0),
                      child: OverflowBar(
                        spacing: 8.0,
                        alignment: MainAxisAlignment.spaceBetween,
                        children: [
                          TextButton(
                            onPressed: () {
                              Navigator.pop(context);
                            },
                            child: Text('إلغاء', style: AppStyles.labelMedium.copyWith(color: AppColors.textSecondary)),
                          ),
                          ElevatedButton(
                            onPressed: () {
                              setState(() {
                                _filterStatus = tempStatus;
                                _filterPriority = tempPriority;
                                _filterAssignee = tempAssignee;
                                _filterHasSubtasks = tempHasSubtasks;
                                _filterMinProgress = tempMinProgress;
                                _filterMaxProgress = tempMaxProgress;
                                _filterDueDateFrom = tempDueDateFrom;
                                _filterDueDateTo = tempDueDateTo;
                                _filterCreationDateFrom = tempCreationDateFrom;
                                _filterCreationDateTo = tempCreationDateTo;
                                _filterTaskType = tempTaskType;
                                _filterTags = tempTags;
                                _sortBy = tempSortBy;
                                _sortDirection = tempSortDirection;
                              });
                              Navigator.pop(context);
                            },
                            style: AppStyles.primaryButtonStyle,
                            child: Text('تطبيق', style: AppStyles.labelMedium.copyWith(color: AppColors.surface)),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }


  /// الحصول على نسبة التقدم للمهمة
  double _getTaskProgress(Task task) {
    return task.completionPercentage / 100;
  }

  /// الحصول على لون التقدم بناءً على النسبة
  Color _getProgressColor(double progress) {
    if (progress < 0.3) {
      return AppColors.error;
    } else if (progress < 0.7) {
      return AppColors.warning;
    } else {
      return AppColors.success;
    }
  }

  /// تطبيق التصفية على المهام
  List<Task> _applyFilters(List<Task> tasks) {

    // التحقق من وجود فلاتر مطبقة
    final hasFilters = _filterStatus != null ||
        _filterPriority != null ||
        _filterAssignee != null ||
        _filterHasSubtasks ||
        _filterMinProgress != null ||
        _filterMaxProgress != null ||
        _filterDueDateFrom != null ||
        _filterDueDateTo != null ||
        _filterCreationDateFrom != null ||
        _filterCreationDateTo != null ||
        _filterTaskType != null ||
        _filterTags.isNotEmpty;

    // إذا لم تكن هناك فلاتر مطبقة، نعيد جميع المهام
    if (!hasFilters) {
      return _applySorting(tasks);
    }

    // التحقق من وجود مهام قبل التصفية
    if (tasks.isEmpty) {
      return tasks;
    }

    // طباعة معلومات التصفية للتشخيص
    // foundation.debugPrint(
    //     'تطبيق الفلاتر: الحالة=$_filterStatus، الأولوية=$_filterPriority، المسؤول=$_filterAssignee');
    // foundation.debugPrint('عدد المهام قبل التصفية: ${tasks.length}');

    final filteredTasks = tasks.where((task) {
      // تصفية حسب الحالة
      if (_filterStatus != null && task.status != _filterStatus) {
        return false;
      }

      // تصفية حسب الأولوية
      if (_filterPriority != null && task.priority != _filterPriority) {
        return false;
      }

      // تصفية حسب المسؤول
      if (_filterAssignee != null) {
        if (_filterAssignee == -1) { // استخدام -1 للإشارة إلى "مهامي"
          final currentUser = Get.find<AuthController>().currentUser.value;
          if (currentUser == null || task.assigneeId != currentUser.id) {
            return false;
          }
        } else if (task.assigneeId != _filterAssignee) {
          return false;
        }
      }

      // تصفية حسب وجود مهام فرعية
      if (_filterHasSubtasks) {
        // تحقق من الخاصية المباشرة في نموذج المهمة
        if (task.subtasks.isEmpty) {
          // إذا لم توجد مهام فرعية في الخاصية، جرب SubtasksController إذا كان مسجلاً
          if (Get.isRegistered<SubtasksController>()) {
            final subtasksController = Get.find<SubtasksController>();
            final hasSubtasks = subtasksController.allSubtasks.any((subtask) => subtask.taskId == task.id);
            if (!hasSubtasks) {
              return false;
            }
          } else {
            // إذا لم يكن هناك طريقة للتحقق، اعتبر أنه لا توجد مهام فرعية
            return false;
          }
        }
      }

      // تصفية حسب نسبة التقدم
      final progress = _getTaskProgress(task);
      if (_filterMinProgress != null && progress < _filterMinProgress!) {
        return false;
      }
      if (_filterMaxProgress != null && progress > _filterMaxProgress!) {
        return false;
      }

      // تصفية حسب تاريخ الاستحقاق
      if (_filterDueDateFrom != null && task.dueDate != null) {
        final taskDueDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
        if (taskDueDate.isBefore(_filterDueDateFrom!)) {
          return false;
        }
      }
      if (_filterDueDateTo != null && task.dueDate != null) {
        final taskDueDate = DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000);
        if (taskDueDate.isAfter(_filterDueDateTo!)) {
          return false;
        }
      }

      // تصفية حسب تاريخ الإنشاء
      if (_filterCreationDateFrom != null) {
        final taskCreatedAt = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
        if (taskCreatedAt.isBefore(_filterCreationDateFrom!)) {
          return false;
        }
      }
      if (_filterCreationDateTo != null) {
        final taskCreatedAt = DateTime.fromMillisecondsSinceEpoch(task.createdAt * 1000);
        if (taskCreatedAt.isAfter(_filterCreationDateTo!)) {
          return false;
        }
      }

      // تصفية حسب نوع المهمة
      if (_filterTaskType != null && task.taskTypeId != _filterTaskType) {
        return false;
      }

      // تصفية حسب الوسوم - مؤقتاً معطلة لأن Task لا يحتوي على tags
      // if (_filterTags.isNotEmpty && task.tags != null) {
      //   bool hasMatchingTag = false;
      //   for (final tag in _filterTags) {
      //     if (task.tags!.contains(tag)) {
      //       hasMatchingTag = true;
      //       break;
      //     }
      //   }
      //   if (!hasMatchingTag) {
      //     return false;
      //   }
      // }

      return true;
    }).toList();

    // طباعة عدد المهام بعد التصفية للتشخيص
    foundation.debugPrint('عدد المهام بعد التصفية: ${filteredTasks.length}');

    // إذا كانت نتيجة التصفية فارغة ولكن كانت هناك مهام قبل التصفية، نعرض رسالة
    if (filteredTasks.isEmpty && tasks.isNotEmpty) {
      // استخدام Future.microtask لتجنب تداخل setState مع بناء الواجهة
      Future.microtask(() {
        Get.snackbar(
          'تنبيه',
          'لا توجد مهام تطابق معايير التصفية المحددة',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.warning.withAlpha(51),
          colorText: AppColors.warning,
          duration: const Duration(seconds: 3),
        );
      });
    }

    // تطبيق الترتيب على المهام المصفاة
    return _applySorting(filteredTasks);
  }

  /// إلغاء جميع المرشحات (الفلاتر)
  void _clearAllFilters() {
    setState(() {
      _filterStatus = null;
      _filterPriority = null;
      _filterAssignee = null;
      _filterHasSubtasks = false;
      _filterMinProgress = null;
      _filterMaxProgress = null;
      _filterDueDateFrom = null;
      _filterDueDateTo = null;
      _filterCreationDateFrom = null;
      _filterCreationDateTo = null;
      _filterTaskType = null;
      _filterTags = [];
      _sortBy = 'dueDate';
      _sortDirection = 'asc';
    });

    // إعادة تحميل المهام بعد إلغاء المرشحات
    _loadTasks(forceRefresh: false); // إلغاء المرشحات لا يحتاج forceRefresh

    // عرض رسالة للمستخدم
    Get.snackbar(
      'تم إلغاء المرشحات',
      'تم إلغاء جميع المرشحات وإعادة ضبط الترتيب',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.success.withAlpha(51),
      colorText: AppColors.success,
      duration: const Duration(seconds: 2),
    );
  }

  /// التحقق من وجود فلاتر نشطة
  bool _hasActiveFilters() {
    return _filterStatus != null ||
        _filterPriority != null ||
        _filterAssignee != null ||
        _filterHasSubtasks ||
        _filterMinProgress != null ||
        _filterMaxProgress != null ||
        _filterDueDateFrom != null ||
        _filterDueDateTo != null ||
        _filterCreationDateFrom != null ||
        _filterCreationDateTo != null ||
        _filterTaskType != null ||
        _filterTags.isNotEmpty ||
        _sortBy != 'dueDate' ||
        _sortDirection != 'asc';
  }

  /// الحصول على عدد الفلاتر النشطة
  int _getActiveFiltersCount() {
    int count = 0;

    if (_filterStatus != null) count++;
    if (_filterPriority != null) count++;
    if (_filterAssignee != null) count++;
    if (_filterHasSubtasks) count++;
    if (_filterMinProgress != null) count++;
    if (_filterMaxProgress != null) count++;
    if (_filterDueDateFrom != null) count++;
    if (_filterDueDateTo != null) count++;
    if (_filterCreationDateFrom != null) count++;
    if (_filterCreationDateTo != null) count++;
    if (_filterTaskType != null) count++;
    if (_filterTags.isNotEmpty) count++;
    if (_sortBy != 'dueDate') count++;
    if (_sortDirection != 'asc') count++;

    return count;
  }

  /// تطبيق الترتيب على المهام
  List<Task> _applySorting(List<Task> tasks) {
    if (tasks.isEmpty) {
      return tasks;
    }

    final sortedTasks = List<Task>.from(tasks);

    switch (_sortBy) {
      case 'title':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.title.compareTo(b.title)
              : b.title.compareTo(a.title);
        });
        break;
      case 'dueDate':
        sortedTasks.sort((a, b) {
          if (a.dueDate == null && b.dueDate == null) return 0;
          if (a.dueDate == null) return _sortDirection == 'asc' ? 1 : -1;
          if (b.dueDate == null) return _sortDirection == 'asc' ? -1 : 1;
          return _sortDirection == 'asc'
              ? a.dueDate!.compareTo(b.dueDate!)
              : b.dueDate!.compareTo(a.dueDate!);
        });
        break;
      case 'createdAt':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.createdAt.compareTo(b.createdAt)
              : b.createdAt.compareTo(a.createdAt);
        });
        break;
      case 'priority':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.priority.compareTo(b.priority)
              : b.priority.compareTo(a.priority);
        });
        break;
      case 'status':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.status.compareTo(b.status)
              : b.status.compareTo(a.status);
        });
        break;
      case 'progress':
        sortedTasks.sort((a, b) {
          return _sortDirection == 'asc'
              ? a.completionPercentage.compareTo(b.completionPercentage)
              : b.completionPercentage.compareTo(a.completionPercentage);
        });
        break;
    }

    return sortedTasks;
  }

  /// بناء عرض المهام المجمعة حسب الحالة
  Widget _buildGroupedTasksView(List<Task> tasks) {
    // طباعة معلومات تشخيصية
    // debugPrint('🔍 _buildGroupedTasksView: عدد المهام المدخلة: ${tasks.length}');

    // تطبيق التصفية
    final filteredTasks = _applyFilters(tasks);
    // debugPrint('🔍 _buildGroupedTasksView: عدد المهام بعد التصفية: ${filteredTasks.length}');

    // عرض رسالة إذا لم توجد مهام بعد التصفية
    if (filteredTasks.isEmpty) {
      debugPrint('⚠️ لا توجد مهام بعد التصفية');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.task_alt,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مهام',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك إنشاء مهمة جديدة بالضغط على زر +',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    // تجميع المهام حسب الحالة
    final Map<String, List<Task>> groupedTasks = {};

    for (final task in filteredTasks) {
      // تطبيع حالة المهمة للتأكد من التطابق
      final normalizedStatus = _normalizeTaskStatus(task.status);

      if (!groupedTasks.containsKey(normalizedStatus)) {
        groupedTasks[normalizedStatus] = [];
      }
      groupedTasks[normalizedStatus]!.add(task);

      // debugPrint('📋 مهمة "${task.title}" (حالة أصلية: "${task.status}") مجمعة في حالة: $normalizedStatus');
    }

    // debugPrint('🗂️ المجموعات المتكونة: ${groupedTasks.keys.toList()}');
    // for (final entry in groupedTasks.entries) {
    //   debugPrint('   ${entry.key}: ${entry.value.length} مهمة');
    // }

    // ترتيب مجموعات الحالة بترتيب محدد
    final allStatuses = [
      'in_progress',
      'pending',
      'waiting_for_info',
      'completed',
      'cancelled',
    ];

    // إضافة الحالات غير الموجودة في المجموعات الحالية
    for (final status in allStatuses) {
      if (!groupedTasks.containsKey(status)) {
        groupedTasks[status] = [];
      }
    }

    // ترتيب الحالات حسب الترتيب المحدد
    final sortedStatuses = allStatuses.where((status) =>
      groupedTasks.containsKey(status)
    ).toList();

    // debugPrint('📊 الحالات المرتبة: $sortedStatuses');

    // استخدام ListView بدلاً من CustomScrollView لتعطيل ميزة السحب للأسفل
    return ListView.builder(
      physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
      itemCount: sortedStatuses.length,
      itemBuilder: (context, index) {
        final status = sortedStatuses[index];
        final statusTasks = groupedTasks[status]!;

        //debugPrint('🏗️ بناء مجموعة $status مع ${statusTasks.length} مهمة');

        // استخدام RepaintBoundary لتحسين أداء الرسم
        return RepaintBoundary(
          child: _buildStatusGroup(status, statusTasks),
        );
      },
    );
  }

  /// بناء مجموعة مهام حسب الحالة
  Widget _buildStatusGroup(String status, List<Task> tasks) {
    // إنشاء منطقة إفلات للمهام
    return DragTarget<Task>(
      builder: (context, candidateData, rejectedData) {
        // تغيير لون الخلفية عند سحب مهمة فوق المجموعة
        final isHovering = candidateData.isNotEmpty;

        return Card(
          margin: const EdgeInsets.all(8),
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(
              color: AppColors.getTaskStatusColor(status),
              width:
                  isHovering ? 2 : 1, // زيادة سمك الحدود عند السحب فوق المجموعة
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // عنوان المجموعة
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isHovering
                      ? AppColors.getTaskStatusColor(status)
                          .withAlpha(50) // تغيير لون الخلفية عند السحب
                      : AppColors.getTaskStatusColor(status)
                          .withAlpha(25),
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(8),
                    topRight: Radius.circular(8),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          getStatusIcon(status),
                          color: AppColors.getTaskStatusColor(status),
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '${getStatusText(status)} (${tasks.length})',
                          style: AppStyles.titleMedium.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.getTaskStatusColor(status),
                          ),
                        ),
                      ],
                    ),
                    // زر إضافة مهمة لهذه المجموعة
                    if (_permissionService.canCreateTask())
                      IconButton(
                        icon: Icon(Icons.add, color: AppColors.primary),
                        tooltip: 'إضافة مهمة جديدة',
                        onPressed: () {
      // الانتقال إلى شاشة إنشاء مهمة مع تحديد الحالة مسبقًا
      Get.to(() => create_task_screen.CreateTaskScreen(initialStatus: task_enums.TaskStatus.fromName(status)))
          ?.then((_) => _loadTasks());
                        },
                      ),
                  ],
                ),
              ),

              // المهام في هذه المجموعة
              ReorderableListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: tasks.length,
                onReorder: (oldIndex, newIndex) {
                  // تعديل المؤشر إذا كان المؤشر الجديد بعد المؤشر القديم
                  if (newIndex > oldIndex) {
                    newIndex -= 1;
                  }

                  // تحريك المهمة في القائمة
                  setState(() {
                    final taskController = Get.find<TaskController>();
                    final allTasks = taskController.allTasks;

                    // الحصول على المهمة التي تم تحريكها
                    final task = tasks[oldIndex];

                    // إعادة ترتيب المهام في القائمة المحلية
                    final updatedTasks = List<Task>.from(tasks);
                    final movedTask = updatedTasks.removeAt(oldIndex);
                    updatedTasks.insert(newIndex, movedTask);

                    // تحديث قائمة المهام الرئيسية
                    for (int i = 0; i < allTasks.length; i++) {
                      if (allTasks[i].id == task.id) {
                        // تحديث المهمة في القائمة الرئيسية
                        allTasks[i] = task;
                        break;
                      }
                    }

                    // تحديث واجهة المستخدم
                    _loadTasks();
                  });

                  // تأثير اهتزاز عند إعادة الترتيب
                  HapticFeedback.lightImpact();
                },
                itemBuilder: (context, index) {
                  return KeyedSubtree(
                    key: ValueKey(tasks[index].id),
                    child: _buildTaskRow(tasks[index]),
                  );
                },
              ),

              // إضافة منطقة فارغة للإفلات إذا كانت القائمة فارغة
              if (tasks.isEmpty)
                Container(
                  height: 100,
                  alignment: Alignment.center,
                  child: Text(
                    'اسحب المهام هنا',
                    style: AppStyles.bodyMedium.copyWith(
                      color: AppColors.textSecondary,
                      fontStyle: FontStyle.italic,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      // قبول المهمة المسحوبة وتغيير حالتها
      onAcceptWithDetails: (DragTargetDetails<Task> details) async {
        final task = details.data;

        // التحقق من صلاحية تغيير حالة المهام
        final permissionService = Get.find<UnifiedPermissionService>();
        if (!permissionService.canChangeTaskStatus()) {
          Get.snackbar(
            'غير مسموح',
            'ليس لديك صلاحية لتغيير حالة المهام',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error.withAlpha(51),
            colorText: AppColors.error,
          );
          return;
        }

        // تجنب تغيير الحالة إذا كانت المهمة بنفس الحالة
        if (task.status != status) {
          final taskController = Get.find<TaskController>();
          // final authController = Get.find<AuthController>(); // غير مستخدم

          // إضافة تأثير اهتزاز عند قبول الإفلات
          HapticFeedback.mediumImpact();

          // عرض مؤشر تحميل
          // Get.dialog(
          //   const Center(
          //     child: CircularProgressIndicator(),
          //   ),
          //   barrierDismissible: false,
          // );

          // طباعة معلومات تشخيصية
          // foundation.debugPrint('محاولة تغيير حالة المهمة: ${task.id}');
          // foundation.debugPrint(
          //     'المستخدم الحالي: ${authController.currentUser.value!.id}');
          // foundation.debugPrint('الحالة الجديدة: $status');

          // تحديث حالة المهمة
          final result = await taskController.changeTaskStatus(task.id, status);

          // طباعة نتيجة العملية
          // foundation.debugPrint('نتيجة تحديث حالة المهمة: $result');
          // if (!result && taskController.error.isNotEmpty) {
          //   foundation.debugPrint('رسالة الخطأ: ${taskController.error}');
          // }

          // إغلاق مؤشر التحميل
          Get.back();

          // إعادة تحميل المهام بعد التحديث مع تأخير قصير
          if (result) {
            // تأخير قصير للتأكد من اكتمال عملية التحديث في قاعدة البيانات
            await Future.delayed(const Duration(milliseconds: 300));
            await _loadTasks();

            // عرض رسالة نجاح
            Get.snackbar(
              'تم بنجاح',
              'تم تغيير حالة المهمة إلى ${getStatusText(status)}',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: AppColors.success.withAlpha(51),
              colorText: AppColors.success,
              duration: const Duration(seconds: 2),
            );
          } else {
            // عرض رسالة خطأ مع التفاصيل
            Get.snackbar(
              'خطأ',
              taskController.error.isNotEmpty
                  ? 'فشل تغيير حالة المهمة: ${taskController.error}'
                  : 'فشل تغيير حالة المهمة',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: AppColors.error.withAlpha(51),
              colorText: AppColors.error,
              duration: const Duration(seconds: 3),
            );
          }

          // تحديث واجهة المستخدم
          setState(() {});
        }
      },
    );
  }

  /// بناء جدول المهام
  Widget _buildTasksTable(List<Task> tasks) {
    // طباعة معلومات تشخيصية
    debugPrint('📊 _buildTasksTable: عدد المهام المدخلة: ${tasks.length}');

    // تطبيق التصفية أولاً
    final filteredTasks = _applyFilters(tasks);
    // debugPrint('📊 _buildTasksTable: عدد المهام بعد التصفية: ${filteredTasks.length}');

    if (filteredTasks.isEmpty) {
      debugPrint('⚠️ لا توجد مهام في جدول المهام');
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_view,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا توجد مهام',
              style: AppStyles.titleMedium.copyWith(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك إنشاء مهمة جديدة بالضغط على زر +',
              style: AppStyles.bodyMedium.copyWith(
                color: AppColors.textHint,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // رأس الجدول - محسن بألوان وتدرجات جميلة
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
          decoration: BoxDecoration(
            // استخدام لون ثابت من AppColors بدلاً من التدرج
            color: AppColors.primaryLight,
            border: Border(
              bottom: BorderSide(
                color: AppColors.primaryDark,
                width: 2,
              ),
            ),
            borderRadius: const BorderRadius.all(Radius.circular(8)),
            // إضافة ظل جميل باستخدام دالة AppColors
            boxShadow: [
              BoxShadow(
                color: AppColors.getShadowColor(0.2),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // عمود رقم المهمة - مع أيقونة مميزة
              Expanded(
                flex: 1,
                child: Row(
                  children: [
                    Icon(
                      Icons.tag,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'رقم المهمة',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText, // استخدام لون النص من AppColors
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود العنوان - مع أيقونة مناسبة
              Expanded(
                flex: 4,
                child: Row(
                  children: [
                    Icon(
                      Icons.title,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'العنوان',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText, // استخدام لون النص من AppColors
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود الحالة - مع أيقونة مناسبة
              Expanded(
                flex: 2,
                child: Row(
                  children: [
                    Icon(
                      Icons.track_changes,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'الحالة',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود الأولوية - مع أيقونة مناسبة
              Expanded(
                flex: 2,
                child: Row(
                  children: [
                    Icon(
                      Icons.priority_high,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'الأولوية',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود تاريخ الاستحقاق - محسن بلون أبيض
              Expanded(
                flex: 3,
                child: Row(
                  children: [
                    Icon(
                      Icons.schedule,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'تاريخ الاستحقاق',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود المالك - مع أيقونة مناسبة
              Expanded(
                flex: 1,
                child: Row(
                  children: [
                    Icon(
                      Icons.person,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'المالك',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود المرفقات - محسن بلون أبيض
              Expanded(
                flex: 1,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.attach_file,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'المرفقات',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
              // عمود التقدم - مع أيقونة مناسبة
              Expanded(
                flex: 3,
                child: Row(
                  children: [
                    Icon(
                      Icons.trending_up,
                      size: 16,
                      color: AppColors.tableHeaderText,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        'التقدم',
                        style: AppStyles.labelMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.tableHeaderText,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),

        // محتوى الجدول
        Expanded(
          child: ListView.builder(
            physics: const ClampingScrollPhysics(), // تعطيل ميزة السحب للأسفل
            itemCount: filteredTasks.length,
            itemBuilder: (context, index) {
              final task = filteredTasks[index];
              return _buildTaskRow(task);
            },
          ),
        ),
      ],
    );
  }

  /// بناء صف مهمه
  Widget _buildTaskRow(Task task) {
    final taskController = Get.find<TaskController>();
    final userController=Get.find<UserController>();
    final isSelected = task.id == _selectedTaskId;
  final creator = userController.getUserById(task.creatorId);
    // بناء محتوى صف المهمة
    Widget taskContent = Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isSelected ? AppColors.primary.withAlpha(26) : AppColors.card,
        border: Border(
          bottom: BorderSide(color: AppColors.border),
        ),
      ),
      child: Row(
        children: [
          // عمود رقم المهمة - إضافة جديدة
          Expanded(
            flex: 1,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
              decoration: BoxDecoration(
                color: AppColors.primary.withAlpha(26),
                borderRadius: BorderRadius.circular(4),
                border: Border.all(
                  color: AppColors.primary.withAlpha(102),
                  width: 1,
                ),
              ),
              child: Text(
                '#${task.id}',
                style: AppStyles.labelMedium.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppColors.primary,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ),

          // عمود العنوان
          Expanded(
            flex: 4,
            child: Row(
              children: [
                // عرض عدد المهام الفرعية (مؤقتاً معطل حتى يتم تنفيذ SubtasksController)
                // TODO: تنفيذ SubtasksController وإضافة الطرق المطلوبة
                const SizedBox.shrink(),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // عنوان المهمة
                      Text(
                        task.title,
                        overflow: TextOverflow.ellipsis,
                        style: AppStyles.titleMedium.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppColors.textPrimary,
                        ),
                      ),

                      // منشئ المهمة (مؤقتاً معطل حتى يتم إصلاح UserController)
                      // TODO: إصلاح UserController.getUserById لترجع Future<User?>
                      const SizedBox.shrink(),
                    ],
                  ),
                ),

                // المرفقات (مؤقتاً معطل حتى يتم إضافة دعم المرفقات في نموذج Task)
                // TODO: إضافة دعم المرفقات في نموذج Task
                const SizedBox.shrink(),

                // المحادثات غير المقروءة (مؤقتاً معطل حتى يتم تنفيذ MessagesController)
                // TODO: تنفيذ MessagesController وإضافة الطرق المطلوبة
                const SizedBox.shrink(),
              ],
            ),
          ),

          // عمود الحالة
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              decoration: BoxDecoration(
                color: AppColors.getTaskStatusColor(task.status)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize:   MainAxisSize.min,
                children: [
                  Icon(
                    getStatusIcon(task.status),
                    size: 16,
                    color: AppColors.getTaskStatusColor(task.status),
                  ),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      getStatusText(task.status),
                      style: AppStyles.labelSmall.copyWith(
                        color: AppColors.getTaskStatusColor(task.status),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // عمود الأولوية
          Expanded(
            flex: 2,
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 8,
                vertical: 4,
              ),
              margin: const EdgeInsets.symmetric(horizontal: 4),
              decoration: BoxDecoration(
                color: AppColors.getTaskPriorityColor(task.priority)
                    .withAlpha(26),
                borderRadius: BorderRadius.circular(4),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                    const SizedBox(width: 4),
                    // أيقونة الأولوية موحدة
                    Icon(TaskDetailHelpers.getPriorityIconByName(task.priority),
                        size: 16,
                        color: AppColors.getTaskPriorityColor(task.priority)),
                 
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      getPriorityText(task.priority),
                      style: AppStyles.labelSmall.copyWith(
                        color: AppColors.getTaskPriorityColor(task.priority),
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // عمود تاريخ الاستحقاق
          Expanded(
            flex: 3,
            child: Text(
              task.dueDate != null
                  ? DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))
                  : 'غير محدد',
              style: AppStyles.labelSmall.copyWith(
                color: task.dueDate != null &&
                        DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000).isBefore(DateTime.now())
                    ? AppColors.textSecondary
                    : AppColors.errorDark,
              ),
            ),
          ),

 // عمود المالك 
          Expanded(
            flex: 1,
          child:  Row(
                    children: [
                      Icon(
                        Icons.person,
                        size: 10,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 2),
                      Expanded(
                        child: Text(
                          creator!.name,
                          style: AppStyles.labelSmall.copyWith(
                            fontSize: 10,
                            color: AppColors.textSecondary,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  )
            // child: Text(
            //   task.dueDate != null
            //       ? DateFormatter.formatDate(DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000))
            //       : 'غير محدد',
            //   style: TextStyle(
            //     color: task.dueDate != null &&
            //             DateTime.fromMillisecondsSinceEpoch(task.dueDate! * 1000).isBefore(DateTime.now())
            //         ? Colors.red
            //         : Colors.grey[600],
            //     fontSize: 12,
            //   ),
            // ),
          ),

          // عمود المرفقات - عرض العدد الفعلي للمرفقات
          Expanded(
            flex: 1,
            child: Center(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: task.attachments.isNotEmpty
                      ? AppColors.attachmentBackground.withAlpha(51)
                      : AppColors.background,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: task.attachments.isNotEmpty
                        ? AppColors.attachmentActive
                        : AppColors.border,
                    width: 1,
                  ),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      task.attachments.isNotEmpty 
                          ? Icons.attach_file 
                          : Icons.attach_file_outlined,
                      size: 12,
                      color: task.attachments.isNotEmpty
                          ? AppColors.attachmentActive
                          : AppColors.attachmentInactive,
                    ),
                    const SizedBox(width: 2),
                    Text(
                      '${task.attachments.length}',
                      style: AppStyles.labelSmall.copyWith(
                        color: task.attachments.isNotEmpty
                            ? AppColors.attachmentActive
                            : AppColors.attachmentInactive,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),

          // عمود التقدم
          Expanded(
            flex: 3,
            child: Stack(
              alignment: Alignment.center,
              children: [
                // شريط التقدم
                LinearPercentIndicator(
                  lineHeight: 16, // زيادة ارتفاع الشريط ليناسب النص
                  percent: _getTaskProgress(task),
                  
                  backgroundColor: AppColors.progressBackground,
                  progressColor: _getProgressColor(_getTaskProgress(task)),
                  barRadius: const Radius.circular(4),
                  padding: const EdgeInsets.symmetric(horizontal: 0),
                  // إزالة trailing لمنع التداخل
                ),
                // نص النسبة المئوية في وسط الشريط
                Text(
                  '${task.completionPercentage.toInt()}%',
                  style: AppStyles.labelSmall.copyWith(
                    color: AppColors.textPrimary,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    shadows: [
                      Shadow(
                        color: AppColors.getShadowColor(0.5),
                        blurRadius: 2,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );

    // جعل المهمة قابلة للسحب فقط في عرض المجموعات
    if (_isGroupedByStatus && !_isListView) {
      return Draggable<Task>(
        // بيانات المهمة التي سيتم نقلها
        data: task,
        // المهمة أثناء السحب (العنصر المرئي الذي يتحرك مع المؤشر)
        feedback: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            width: MediaQuery.of(context).size.width * 0.7,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.card,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.getTaskStatusColor(task.status),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.getShadowColor(0.1),
                  blurRadius: 10,
                  spreadRadius: 1,
                ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 12,
                  height: 12,
                  decoration: BoxDecoration(
                    color: AppColors.getTaskPriorityColor(task.priority),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    task.title,
                    style: AppStyles.titleMedium.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppColors.textPrimary,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                // عرض الحالة
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.getTaskStatusColor(task.status)
                        .withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        getStatusIcon(task.status),
                        size: 16,
                        color: AppColors.getTaskStatusColor(task.status),
                      ),
                      const SizedBox(width: 4),
                      Text(
                        getStatusText(task.status),
                        style: AppStyles.labelSmall.copyWith(
                          color: AppColors.getTaskStatusColor(task.status),
                        ),
                    ),
               ] ),
                ),
                const SizedBox(width: 4),
                // عرض الأولوية
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: AppColors.getTaskPriorityColor(task.priority)
                        .withAlpha(26),
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    getPriorityText(task.priority),
                    style: AppStyles.labelSmall.copyWith(
                      color: AppColors.getTaskPriorityColor(task.priority),
                    ),
                  ),
                ),
                const SizedBox(width: 4),
                // عرض المرفقات (مؤقتاً معطل حتى يتم إضافة دعم المرفقات)
                // TODO: إضافة دعم المرفقات في نموذج Task
                const SizedBox.shrink(),
              ],
            ),
          ),
        ),
        // المهمة عندما تكون قيد السحب (العنصر الذي يظل في مكانه الأصلي)
        childWhenDragging: Opacity(
          opacity: 0.3,
          child: taskContent,
        ),
        // تأثير الرسوم المتحركة عند بدء السحب
        onDragStarted: () {
          // يمكن إضافة تأثيرات صوتية أو اهتزاز هنا إذا لزم الأمر
          HapticFeedback.lightImpact(); // اهتزاز خفيف عند بدء السحب
        },
        // تأثير الرسوم المتحركة عند انتهاء السحب
        onDragEnd: (details) {
          // يمكن إضافة تأثيرات صوتية أو اهتزاز هنا إذا لزم الأمر
          if (details.wasAccepted) {
            HapticFeedback.mediumImpact(); // اهتزاز متوسط عند قبول الإفلات
          }
        },
        // المهمة في حالتها العادية
        child: DragTarget<TaskPriority>(
          // قبول إفلات الأولوية على المهمة
          onAcceptWithDetails: (details) async {
            final newPriority = details.data;

            // التحقق من صلاحية تغيير أولوية المهام
            final permissionService = Get.find<UnifiedPermissionService>();
            if (!permissionService.canChangeTaskPriority()) {
              Get.snackbar(
                'غير مسموح',
                'ليس لديك صلاحية لتغيير أولوية المهام',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: AppColors.error.withAlpha(51),
                colorText: AppColors.error,
              );
              return;
            }

            // تحويل enum level إلى string value
            String priorityId = _getPriorityIdFromLevel(newPriority.level);

            // تجنب تحديث الأولوية إذا كانت نفس الأولوية الحالية
            if (task.priority != priorityId) {
              final taskController = Get.find<TaskController>();
              final authController = Get.find<AuthController>();

              // إضافة تأثير اهتزاز عند قبول الإفلات
              HapticFeedback.mediumImpact();

              // // عرض مؤشر تحميل
              // Get.dialog(
              //   const Center(
              //     child: CircularProgressIndicator(),
              //   ),
              //   barrierDismissible: false,
              // );

              // تحديث أولوية المهمة
              // final result = await taskController.updateTaskPriority(
              await taskController.updateTaskPriority(
                task.id,
                authController.currentUser.value!.id,
                newPriority.name,
              );

              // إغلاق مؤشر التحميل
              Get.back();

              // إعادة تحميل المهام بعد التحديث مع تأخير قصير
              // تأخير قصير للتأكد من اكتمال عملية التحديث في قاعدة البيانات
              await Future.delayed(const Duration(milliseconds: 300));
              await _loadTasks();

              // عرض رسالة نجاح
              Get.snackbar(
                'تم بنجاح',
                'تم تغيير أولوية المهمة إلى ${getPriorityText(newPriority)}',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: AppColors.success.withAlpha(51),
                colorText: AppColors.success,
                duration: const Duration(seconds: 2),
              );

              // تحديث واجهة المستخدم
              setState(() {});
            }
          },
          builder: (context, candidateData, rejectedData) {
            // تغيير مظهر المهمة عند سحب أولوية فوقها
            final isHovering = candidateData.isNotEmpty;

            return AnimatedContainer(
              duration: const Duration(milliseconds: 200),
              decoration: isHovering
                  ? BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      border: Border.all(
                        color: AppColors.primary,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primary.withAlpha(77),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    )
                  : null,
              child: InkWell(
                onTap: _permissionService.canViewTaskDetails() ? () {
                   foundation.debugPrint(
                      'تم النقر على المهمة: ${task.title} - المعرف: ${task.id}');
                  setState(() {
                    _selectedTaskId = task.id;
                    _showSidePanel = true;
                  });
                  foundation.debugPrint('جاري تحميل تفاصيل المهمة...');
                  taskController.loadTaskDetails(task.id.toString()).then((_) {
                    // foundation.debugPrint('تم تحميل تفاصيل المهمة بنجاح');
                    if (taskController.error.isNotEmpty) {
                      foundation.debugPrint(
                          'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error}');
                      Get.snackbar(
                        'خطأ',
                        'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error}',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: AppColors.error.withAlpha(51),
                        colorText: AppColors.error,
                      );
                    }
                    // تحديث واجهة المستخدم بعد تحميل البيانات
                    taskController.update(['task_details']);
                  }).catchError((error) {
                    foundation.debugPrint('حدث خطأ غير متوقع: $error');
                    Get.snackbar(
                      'خطأ',
                      'حدث خطأ غير متوقع: $error',
                      snackPosition: SnackPosition.BOTTOM,
                      backgroundColor: AppColors.error.withAlpha(51),
                      colorText: AppColors.error,
                    );
                  });
                } : null,
                child: taskContent,
              ),
            );
          },
        ),
      );
    } else {
      // في حالة عدم تفعيل عرض المجموعات، نعرض المهمة بشكل عادي
      return InkWell(
        onTap: _permissionService.canViewTaskDetails() ? () {
          foundation.debugPrint(
              'تم النقر على المهمة (عرض عادي): ${task.title} - المعرف: ${task.id}');
          setState(() {
            _selectedTaskId = task.id;
            _showSidePanel = true;
          });
          // foundation.debugPrint('جاري تحميل تفاصيل المهمة (عرض عادي)...');
          taskController.loadTaskDetails(task.id.toString()).then((_) {
            // foundation.debugPrint('تم تحميل تفاصيل المهمة بنجاح (عرض عادي)');
            if (taskController.error.isNotEmpty) {
              foundation.debugPrint(
                  'حدث خطأ أثناء تحميل تفاصيل المهمة (عرض عادي): ${taskController.error}');
              Get.snackbar(
                'خطأ',
                'حدث خطأ أثناء تحميل تفاصيل المهمة: ${taskController.error}',
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: AppColors.error.withAlpha(51),
                colorText: AppColors.error,
              );
            }
            // تحديث واجهة المستخدم بعد تحميل البيانات
            taskController.update(['task_details']);
          }).catchError((error) {
            foundation.debugPrint('حدث خطأ غير متوقع (عرض عادي): $error');
            Get.snackbar(
              'خطأ',
              'حدث خطأ غير متوقع: $error',
              snackPosition: SnackPosition.BOTTOM,
              backgroundColor: AppColors.error.withAlpha(51),
              colorText: AppColors.error,
            );
          });
        } : null,
        child: taskContent,
      );
    }
  }

  /// بناء أولوية قابلة للسحب
  Widget _buildDraggablePriority(TaskPriority priority) {
    // الحصول على لون ونص وأيقونة الأولوية بشكل موحد
    final priorityColor = TaskDetailHelpers.getPriorityColorByName(priority.name);
    final priorityText = TaskDetailHelpers.getPriorityTextByName(priority.name);
    final priorityIcon = TaskDetailHelpers.getPriorityIconByName(priority.name);

    // إنشاء أولوية قابلة للسحب
    return Draggable<TaskPriority>(
      // بيانات الأولوية التي سيتم نقلها
      data: priority,
      // الأولوية أثناء السحب (العنصر المرئي الذي يتحرك مع المؤشر)
      feedback: Material(
        elevation: 8,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: AppColors.card,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: priorityColor,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.getShadowColor(0.1),
                blurRadius: 10,
                spreadRadius: 1,
              ),
            ],
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                priorityIcon,
                color: priorityColor,
                size: 20,
              ),
              const SizedBox(width: 4),
              Text(
                priorityText,
                style: AppStyles.labelSmall.copyWith(
                  color: priorityColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ),
      ),
      // الأولوية عندما تكون قيد السحب (العنصر الذي يظل في مكانه الأصلي)
      childWhenDragging: Opacity(
        opacity: 0.3,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: priorityColor.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: priorityColor,
              width: 1,
            ),
          ),
          child: Icon(
            priorityIcon,
            color: priorityColor,
            size: 20,
          ),
        ),
      ),
      // تأثير الرسوم المتحركة عند بدء السحب
      onDragStarted: () {
        // يمكن إضافة تأثيرات صوتية أو اهتزاز هنا إذا لزم الأمر
        HapticFeedback.lightImpact(); // اهتزاز خفيف عند بدء السحب
      },
      // الأولوية في حالتها العادية
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: priorityColor.withAlpha(20),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: priorityColor,
            width: 1,
          ),
        ),
        child: Icon(
          priorityIcon,
          color: priorityColor,
          size: 20,
        ),
      ),
    );
  }

  /// بناء منطقة إفلات للأولويات
  Widget _buildPriorityDropTarget(TaskPriority priority) {
    // الحصول على لون ونص وأيقونة الأولوية بشكل موحد
    final priorityColor = TaskDetailHelpers.getPriorityColorByName(priority.name);
    final priorityText = TaskDetailHelpers.getPriorityTextByName(priority.name);
    final priorityIcon = TaskDetailHelpers.getPriorityIconByName(priority.name);

    // إنشاء منطقة إفلات للأولوية
    return DragTarget<Task>(
      builder: (context, candidateData, rejectedData) {
        // تغيير المظهر عند سحب مهمة فوق منطقة الإفلات
        final isHovering = candidateData.isNotEmpty;

        return AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          decoration: BoxDecoration(
            color: isHovering
                ? priorityColor.withAlpha(50)
                : priorityColor.withAlpha(20),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: priorityColor,
              width: isHovering ? 2 : 1,
            ),
            boxShadow: isHovering
                ? [
                    BoxShadow(
                      color: priorityColor.withAlpha(50),
                      blurRadius: 8,
                      spreadRadius: 1,
                    ),
                  ]
                : null,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                priorityIcon,
                color: priorityColor,
                size: 20,
              ),
              const SizedBox(height: 4),
              Text(
                priorityText,
                style: AppStyles.labelSmall.copyWith(
                  color: priorityColor,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        );
      },
      // قبول المهمة المسحوبة وتغيير أولويتها
      onAcceptWithDetails: (DragTargetDetails<Task> details) async {
        final task = details.data;

        // التحقق من صلاحية تغيير أولوية المهام
        final permissionService = Get.find<UnifiedPermissionService>();
        if (!permissionService.canChangeTaskPriority()) {
          Get.snackbar(
            'غير مسموح',
            'ليس لديك صلاحية لتغيير أولوية المهام',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error.withAlpha(51),
            colorText: AppColors.error,
          );
          return;
        }

        // تحويل enum level إلى string value
        String priorityId = _getPriorityIdFromLevel(priority.level);

        // تجنب تحديث الأولوية إذا كانت نفس الأولوية الحالية
        if (task.priority != priorityId) {
          // إضافة تأثير اهتزاز عند قبول الإفلات
          HapticFeedback.mediumImpact();

          // عرض مؤشر تحميل
          Get.dialog(
            const Center(
              child: CircularProgressIndicator(),
            ),
            barrierDismissible: false,
          );

          

          // إغلاق مؤشر التحميل
          Get.back();

          // إعادة تحميل المهام بعد التحديث مع تأخير قصير
          // تأخير قصير للتأكد من اكتمال عملية التحديث في قاعدة البيانات
          await Future.delayed(const Duration(milliseconds: 300));
          await _loadTasks();

          // عرض رسالة نجاح
          Get.snackbar(
            'تم بنجاح',
            'تم تغيير أولوية المهمة إلى ${getPriorityText(priority)}',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.success.withAlpha(51),
            colorText: AppColors.success,
            duration: const Duration(seconds: 2),
          );

          // تحديث واجهة المستخدم
          setState(() {});
        }
      },
    );
  }

  /// بناء لوحة السحب والإفلات
  Widget _buildDragDropBoard(List<Task> tasks) {
    final taskController = Get.find<TaskController>();
    final authController = Get.find<AuthController>();

    // طباعة معلومات تشخيصية
    debugPrint('🎯 _buildDragDropBoard: عدد المهام المدخلة: ${tasks.length}');

    // تطبيق التصفية أولاً
    final filteredTasks = _applyFilters(tasks);
    debugPrint('🎯 _buildDragDropBoard: عدد المهام بعد التصفية: ${filteredTasks.length}');

    // تصنيف المهام حسب الحالة مع تطبيع الحالات
    final pendingTasks = filteredTasks.where((task) {
      final normalizedStatus = _normalizeTaskStatus(task.status);
      return normalizedStatus == 'pending';
    }).toList();

    final inProgressTasks = filteredTasks.where((task) {
      final normalizedStatus = _normalizeTaskStatus(task.status);
      return normalizedStatus == 'in_progress';
    }).toList();

    final waitingTasks = filteredTasks.where((task) {
      final normalizedStatus = _normalizeTaskStatus(task.status);
      return normalizedStatus == 'waiting_for_info';
    }).toList();

    final completedTasks = filteredTasks.where((task) {
      final normalizedStatus = _normalizeTaskStatus(task.status);
      return normalizedStatus == 'completed';
    }).toList();

    final cancelledTasks = filteredTasks.where((task) {
      final normalizedStatus = _normalizeTaskStatus(task.status);
      return normalizedStatus == 'cancelled';
    }).toList();

    // طباعة إحصائيات التصنيف
    debugPrint('📊 تصنيف المهام:');
    debugPrint('   قيد الانتظار: ${pendingTasks.length}');
    debugPrint('   قيد التنفيذ: ${inProgressTasks.length}');
    debugPrint('   في انتظار معلومات: ${waitingTasks.length}');
    debugPrint('   مكتملة: ${completedTasks.length}');
    debugPrint('   ملغاة: ${cancelledTasks.length}');

    return SimpleDragDropBoard(
      pendingTasks: pendingTasks,
      inProgressTasks: inProgressTasks,
      waitingTasks: waitingTasks,
      completedTasks: completedTasks,
      onTaskStatusChanged: (task, newStatus) async {
        // تحديث حالة المهمة
        final result = await taskController.updateTaskStatus(
          task.id,
          authController.currentUser.value!.id,
          newStatus.name,
        );

        // إعادة تحميل المهام بعد التحديث
        if (result) {
          await _loadTasks();
        }
      },
      onTaskTap: (task) {
        // التحقق من صلاحية عرض تفاصيل المهام
        final permissionService = Get.find<UnifiedPermissionService>();
        if (!permissionService.canViewTaskDetails()) {
          Get.snackbar(
            'غير مسموح',
            'ليس لديك صلاحية لعرض تفاصيل المهام',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error.withAlpha(51),
            colorText: AppColors.error,
          );
          return;
        }

        // عرض تفاصيل المهمة
        setState(() {
          _selectedTaskId = task.id;
          _showSidePanel = true;
        });
        taskController.loadTaskDetails(task.id.toString());
      },
      onAddTask: () {
        // التحقق من صلاحية إنشاء المهام
        final permissionService = Get.find<UnifiedPermissionService>();
        if (!permissionService.canCreateTask()) {
          Get.snackbar(
            'غير مسموح',
            'ليس لديك صلاحية لإنشاء مهام جديدة',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error.withAlpha(51),
            colorText: AppColors.error,
          );
          return;
        }

        // إضافة مهمة جديدة
        Get.to(() => const create_task_screen.CreateTaskScreen())?.then((_) => _loadTasks());
      },
      onTaskReordered: (status, reorderedTasks) async {
        // تحديث ترتيب المهام في الكنترولر
        // هذه الوظيفة تحتاج إلى إضافتها في TaskController
        try {
          // تأثير اهتزاز عند إعادة الترتيب
          HapticFeedback.mediumImpact();

          // عرض رسالة للمستخدم
          Get.snackbar(
            'تم إعادة الترتيب',
            'تم إعادة ترتيب المهام بنجاح',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.success.withAlpha(51),
            colorText: AppColors.success,
            duration: const Duration(seconds: 2),
          );

          // إعادة تحميل المهام بعد التحديث
          await _loadTasks();
        } catch (e) {
          // عرض رسالة خطأ للمستخدم
          Get.snackbar(
            'خطأ',
            'حدث خطأ أثناء إعادة ترتيب المهام: $e',
            snackPosition: SnackPosition.BOTTOM,
            backgroundColor: AppColors.error.withAlpha(51),
            colorText: AppColors.error,
          );
        }
      },
    );
  }

  /// تحويل مستوى الأولوية من enum إلى string value
  String _getPriorityIdFromLevel(int level) {
    switch (level) {
      case 1:
        return 'low';
      case 2:
        return 'medium';
      case 3:
        return 'high';
      case 4:
        return 'urgent';
      default:
        return 'medium';
    }
  }

  /// تطبيع حالة المهمة للتعامل مع جميع الاحتمالات
  String _normalizeTaskStatus(String status) {
    final normalizedStatus = status.toLowerCase().trim();

    switch (normalizedStatus) {
      case 'inprogress':
      case 'in_progress':
      case 'in-progress':
      case 'قيد التنفيذ':
        return 'in_progress';

      case 'waitingforinfo':
      case 'waiting_for_info':
      case 'waiting-for-info':
      case 'في انتظار معلومات':
        return 'waiting_for_info';

      case 'pending':
      case 'new':
      case 'news':
      case 'جديد':
      case 'جديدة':
        return 'pending';

      case 'completed':
      case 'done':
      case 'مكتملة':
        return 'completed';

      case 'cancelled':
      case 'canceled':
      case 'ملغاة':
        return 'cancelled';

      default:
        debugPrint('⚠️ حالة غير معروفة: "$status"');
        return status.toLowerCase(); // إرجاع الحالة كما هي مع تحويلها للأحرف الصغيرة
    }
  }
  //   // الأولوية العاجلة غير موجودة حالياً، لذا نستخدم عالية
  //   switch (level) {
  //     case 1: return 3;  // منخفضة
  //     case 2: return 9;  // متوسطة
  //     case 3: return 12; // عالية
  //     case 4: return 12; // عاجلة -> نستخدم عالية مؤقتاً
  //     default: return 9; // افتراضي: متوسطة
  //   }
  // }
}
