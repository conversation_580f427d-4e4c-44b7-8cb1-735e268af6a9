using Microsoft.AspNetCore.SignalR;

namespace webApi.Hubs
{
    public class TaskHub : Hub
    {
        /// <summary>
        /// الانضمام إلى مجموعة مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        public async Task JoinTaskGroup(string taskId)
        {
            var groupName = $"Task_{taskId}";
            await Groups.AddToGroupAsync(Context.ConnectionId, groupName);
            Console.WriteLine($"User {Context.ConnectionId} joined task group {groupName}");
        }

        /// <summary>
        /// مغادرة مجموعة مهمة محددة
        /// </summary>
        /// <param name="taskId">معرف المهمة</param>
        public async Task LeaveTaskGroup(string taskId)
        {
            var groupName = $"Task_{taskId}";
            await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupName);
            Console.WriteLine($"User {Context.ConnectionId} left task group {groupName}");
        }

        /// <summary>
        /// إرسال رسالة إلى مجموعة دردشة محددة
        /// </summary>
        /// <param name="groupId">معرف مجموعة الدردشة</param>
        /// <param name="message">كائن الرسالة</param>
        public async Task<object> SendMessageToGroup(string groupId, object message)
        {
            try
            {
                // بث الرسالة إلى جميع العملاء في المجموعة المحددة
                await Clients.Group(groupId).SendAsync("ReceiveMessage", message);
                // يمكن إضافة منطق لحفظ الرسالة في قاعدة البيانات هنا إذا لم يتم ذلك بالفعل في API
                return new { Success = true, Message = "تم إرسال الرسالة بنجاح" };
            }
            catch (Exception ex)
            {
                return new { Success = false, Message = "خطأ في إرسال الرسالة", Error = ex.Message };
            }
        }

        /// <summary>
        /// إضافة العميل الحالي إلى مجموعة دردشة
        /// </summary>
        /// <param name="groupId">معرف مجموعة الدردشة</param>
        // This method is used to join a user to a group
        public async Task<object> JoinGroup(string groupId)
        {
            try
            {
                // Add the user to the group
                await Groups.AddToGroupAsync(Context.ConnectionId, groupId);
                // يمكن إرسال إشعار للعملاء الآخرين بأن مستخدمًا انضم إلى المجموعة
                await Clients.Group(groupId).SendAsync("UserJoinedGroup", Context.ConnectionId, groupId);
                return new { Success = true, Message = "تم الانضمام بنجاح" };
            }
            catch (Exception ex)
            {
                return new { Success = false, Message = "خطأ في الانضمام", Error = ex.Message };
            }
        }

        /// <summary>
        /// إزالة العميل الحالي من مجموعة دردشة
        /// </summary>
        /// <param name="groupId">معرف مجموعة الدردشة</param>
        // This method is used to leave a group
        public async Task<object> LeaveGroup(string groupId)
        {
            try
            {
                // Remove the user from the group
                await Groups.RemoveFromGroupAsync(Context.ConnectionId, groupId);
                // يمكن إرسال إشعار للعملاء الآخرين بأن مستخدمًا غادر المجموعة
                await Clients.Group(groupId).SendAsync("UserLeftGroup", Context.ConnectionId, groupId);
                return new { Success = true, Message = "تم المغادرة بنجاح" };
            }
            catch (Exception ex)
            {
                return new { Success = false, Message = "خطأ في المغادرة", Error = ex.Message };
            }
        }

        // يمكن إضافة طرق أخرى هنا للتعامل مع أحداث الدردشة الأخرى مثل الكتابة، قراءة الرسائل، إلخ.
    }
}