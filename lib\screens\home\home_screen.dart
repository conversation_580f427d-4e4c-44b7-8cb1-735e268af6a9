import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_application_2/controllers/notifications_controller.dart';
import 'package:flutter_application_2/screens/home/<USER>';
import 'package:flutter_application_2/screens/power_bi/power_bi_screen.dart';
import 'package:get/get.dart';

import '../../controllers/auth_controller.dart';
import '../../controllers/language_controller.dart';
import '../../controllers/task_controller.dart';
import '../../controllers/theme_controller.dart';
import '../../controllers/notification_controller.dart';
import '../../controllers/admin_controller.dart';
import '../../controllers/department_controller.dart';
import '../../controllers/user_controller.dart';

import '../../services/unified_permission_service.dart';
import '../../services/cache_service.dart';
import '../../utils/responsive_helper.dart';
import '../../utils/mouse_event_handler.dart';
import '../../routes/app_routes.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../constants/app_bar_styles.dart';
import '../widgets/search/search_app_bar_action.dart';
import '../../widgets/notifications_button.dart';
import 'tasks_tab.dart';
import 'departments_tab.dart';
import 'notifications_tab.dart';
import 'profile_tab.dart';
import '../chat/unified_chat_list_screen.dart';
import '../admin/admin_dashboard_new.dart';
import '../reports/reports_screen.dart';
import '../calendar/calendar_screen.dart';
import '../widgets/app_drawer.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with RouteAware {
  // مراقب المسارات للتتبع عند العودة إلى الصفحة
  late RouteObserver<PageRoute> _routeObserver;
  int _currentIndex = 0;
  late List<Widget> _tabs;
  late List<bool> _tabPermissions;

  // خدمة الصلاحيات
  final UnifiedPermissionService permissionService = Get.find<UnifiedPermissionService>();

  // قائمة بأسماء الواجهات المقابلة لكل علامة تبويب
  final List<String> _interfaceNames = [
    'dashboard', // الصفحة الرئيسية
    'tasks', // المهام
    'departments', // الأقسام
    'messages', // المحادثات
    'reports', // التقارير
    'notifications', // الإشعارات
    'profile', // الملف الشخصي
    'admin', // لوحة التحكم الإدارية
    'calendar', // التقويم
    'power_bi', // باور بي آي
    'reports2', // باور بي آي
  ];

  // دالة لتغيير التبويب الحالي
  void changeTab(int index) {
    if (index >= 0 && index < _tabs.length && _tabPermissions[index]) {
      setState(() {
        _currentIndex = index;
      });
    }
  }

  // دالة للحصول على أيقونة لكل تبويب - غير مستخدمة حالياً
  // TODO: يمكن استخدام هذه الدالة في المستقبل لتوحيد الأيقونات في التطبيق
  /*
  IconData _getIconForTab(int index) {
    switch (index) {
      case 0: // الرئيسية
        return Icons.dashboard;
      case 1: // المهام
        return Icons.task;
      case 2: // الأقسام
        return Icons.business;
      case 3: // المحادثات
        return Icons.chat;
      case 4: // التقارير
        return Icons.bar_chart;
      case 5: // الإشعارات
        return Icons.notifications;
      case 6: // الملف الشخصي
        return Icons.person;
      case 7: // التقويم
        return Icons.calendar_today;
      case 8: // لوحة التحكم الإدارية
        return Icons.admin_panel_settings;
      case 9: // باور بي آي
        return Icons.analytics;
      default:
        return Icons.circle;
    }
  }
  */

  // قائمة لتخزين الويدجت بشكل مؤقت لتجنب مشاكل تتبع الماوس
  List<Widget> _tempTabs = [];
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    // تنظيف أي أحداث ماوس معلقة وقفلها مؤقتًا لتجنب مشاكل تتبع الماوس
    SafeMouseRegion.clearEvents();
    SafeMouseRegion.lockEvents(2000); // زيادة مدة القفل لضمان استقرار الشاشة

    // تهيئة مصفوفة الصلاحيات بشكل مبدئي
    _tabPermissions = List.filled(11, false); // عدد ثابت لتجنب مشاكل التهيئة
    _tabPermissions[0] = true; // الصفحة الرئيسية متاحة دائمًا
    _tabPermissions[6] = true; // الملف الشخصي متاح دائمًا
    _tabPermissions[7] = true; // التقويم متاح دائمًا

    // تهيئة قائمة فارغة مؤقتة لتجنب مشاكل تتبع الماوس
    _tabs = List.generate(11, (_) => const SizedBox.shrink());

    // استخدام GetX لتتبع التنقل بين الصفحات
    _routeObserver = Get.put(RouteObserver<PageRoute>());

    // تسجيل حالة الشاشة الرئيسية في GetX للوصول إليها من أي مكان
    Get.put(this, tag: 'HomeScreenState');

    // تأخير تهيئة علامات التبويب لتجنب مشاكل تتبع الماوس
    SchedulerBinding.instance.addPostFrameCallback((_) {
      _initializeTabsAndData();
    final notifications =Get.find<NotificationsController>();

    notifications.refresh();

    });
  }

  /// تهيئة علامات التبويب والبيانات بشكل آمن
  Future<void> _initializeTabsAndData() async {
    try {
      // Get auth controller to check if user is admin
      // final authController = Get.find<AuthController>(); // غير مستخدم
      // final notificationController = Get.find<NotificationController>();

      // Listen to changes in the notifications list
      // تم تعطيل ever() مؤقتاً لتجنب مشاكل التوافق
      // ever(notificationController.notifications, (_) {
      //   if (mounted) setState(() {});
      // });

      // تهيئة علامات التبويب بشكل مؤقت لتجنب مشاكل تتبع الماوس
      _tempTabs = [
        const DashboardTab(),
        const TasksTab(),
        const DepartmentsTab(),
        const UnifiedChatListScreen(),
        const ReportsScreen(),
        const NotificationsTab(),
        const ProfileTab(),
        const CalendarScreen(),
        const PowerBIScreen(),
        const ReportsScreen(),
      ];

      // Add admin panel tab if user has admin permissions
      if (permissionService.canAccessAdmin()) {
        _tempTabs.add(const AdminDashboardScreen());
      }

      // تحميل البيانات الأولية
      await _loadInitialData();

      // التحقق من صلاحيات المستخدم للواجهات الأخرى
      await _checkUserPermissions();

      // تأخير تطبيق التغييرات لضمان استقرار الشاشة
      await Future.delayed(const Duration(milliseconds: 1000));

      // تطبيق التغييرات على واجهة المستخدم
      if (mounted) {
        setState(() {
          _tabs = List.from(_tempTabs);
          _isInitialized = true;
        });
      }

      // إلغاء قفل أحداث الماوس بعد تهيئة الشاشة واكتمال تحميل البيانات
      await Future.delayed(const Duration(milliseconds: 500));
      SafeMouseRegion.unlockEvents();
    } catch (e) {
      debugPrint('خطأ في تهيئة علامات التبويب والبيانات: $e');

      // محاولة إلغاء قفل أحداث الماوس في حالة الخطأ
      SafeMouseRegion.unlockEvents();

      // عرض رسالة خطأ للمستخدم
      Get.snackbar(
        'خطأ في التهيئة',
        'حدث خطأ أثناء تهيئة التطبيق. يرجى إعادة تشغيل التطبيق.',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 5),
      );
    }
  }

  /// إعادة تحميل البيانات أو إعادة تشغيل التطبيق
  Future<void> reloadData() async {
    // عرض مؤشر التحميل
    Get.dialog(
      const Center(
        child: CircularProgressIndicator(),
      ),
      barrierDismissible: false,
    );

    try {
      // إعادة تحميل البيانات الأساسية
      await _loadInitialData();

      // إعادة تحميل المهام
      await reloadTasks();

      // إعادة تحميل الإشعارات
      final authController = Get.find<AuthController>();
      final notificationController = Get.find<NotificationController>();
      if (authController.currentUser.value != null) {
        await notificationController
            .loadNotifications(authController.currentUser.value!.id);
      }

      // إعادة تحميل البيانات الإدارية (المستخدمين، الأدوار، الصلاحيات، الأقسام)
      try {
        final adminController = Get.find<AdminController>();
        await adminController.refreshAllData();
        debugPrint('✅ تم تحديث البيانات الإدارية');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على AdminController أو خطأ في التحديث: $e');
      }

      // إعادة تحميل بيانات الأقسام
      try {
        final departmentController = Get.find<DepartmentController>();
        await departmentController.loadAllDepartments();
        debugPrint('✅ تم تحديث بيانات الأقسام');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على DepartmentController أو خطأ في التحديث: $e');
      }

      // إعادة تحميل بيانات المستخدمين
      try {
        final userController = Get.find<UserController>();
        await userController.loadAllUsers();
        debugPrint('✅ تم تحديث بيانات المستخدمين');
      } catch (e) {
        debugPrint('⚠️ لم يتم العثور على UserController أو خطأ في التحديث: $e');
      }

      // إعادة تحميل صلاحيات المستخدم الحالي
      try {
        final permissionService = Get.find<UnifiedPermissionService>();
        await permissionService.refreshCurrentUserPermissions();
        debugPrint('✅ تم تحديث صلاحيات المستخدم');
      } catch (e) {
        debugPrint('⚠️ خطأ في تحديث الصلاحيات: $e');
      }

      // مسح التخزين المؤقت لضمان تحديث البيانات
      try {
        final cacheService = Get.find<CacheService>();
        await cacheService.clear();
        debugPrint('✅ تم مسح التخزين المؤقت');
      } catch (e) {
        debugPrint('⚠️ خطأ في مسح التخزين المؤقت: $e');
      }

      // إعادة تحقق من صلاحيات المستخدم
      await _checkUserPermissions();

      // تحديث واجهة المستخدم
      setState(() {});

      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة نجاح مفصلة
      Get.snackbar(
        'تم بنجاح',
        'تم إعادة تحميل جميع البيانات بنجاح:\n• المهام والمرفقات\n• الإشعارات\n• البيانات الإدارية\n• الأقسام والمستخدمين\n• الصلاحيات والتخزين المؤقت',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green.shade100,
        colorText: Colors.green.shade800,
        duration: const Duration(seconds: 4),
        maxWidth: 400,
      );
    }
    catch (e) {
      // إغلاق مؤشر التحميل
      Get.back();

      // عرض رسالة خطأ
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إعادة تحميل البيانات: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red.shade100,
        colorText: Colors.red.shade800,
        duration: const Duration(seconds: 3),
      );

      debugPrint('خطأ في إعادة تحميل البيانات: $e');
    }
  }

  /// التحقق من صلاحيات المستخدم للواجهات
  Future<void> _checkUserPermissions() async {
    try {
      final authController = Get.find<AuthController>();
      final permissionService = Get.find<UnifiedPermissionService>();

      // لا نعطي صلاحيات شاملة بناءً على الأدوار - كل صلاحية يجب أن تكون محددة
      // تم إزالة المنطق الذي يعطي جميع الصلاحيات للمديرين

      // التحقق من صلاحيات المستخدم لكل واجهة
      if (authController.currentUser.value != null) {
        for (int i = 1; i < _interfaceNames.length && i < _tabs.length; i++) {
          if (i != 0 && i != 6 && i != 7) {
            // تخطي الصفحة الرئيسية والملف الشخصي والتقويم
            try {
              // استخدام خدمة الصلاحيات الموحدة للتحقق من صلاحية الوصول إلى الواجهة
              final hasAccess =
                  await permissionService.checkInterfaceAccess(_interfaceNames[i]);
              debugPrint(
                  'التحقق من صلاحية الوصول إلى: ${_interfaceNames[i]}, النتيجة: $hasAccess');
              if (mounted) {
                setState(() {
                  _tabPermissions[i] = hasAccess;
                });
              }
            } catch (e) {
              debugPrint('خطأ في التحقق من صلاحية ${_interfaceNames[i]}: $e');
              // في حالة الخطأ، لا نعطي أي صلاحية افتراضية
              if (mounted) {
                setState(() {
                  _tabPermissions[i] = false; // لا توجد صلاحيات افتراضية
                });
              }
            }
          }
        }
      }
    } catch (e) {
      debugPrint('خطأ عام في التحقق من الصلاحيات: $e');
      // في حالة الخطأ العام، نعطي جميع الصلاحيات
      if (mounted) {
        setState(() {
          _tabPermissions = List.filled(_tabs.length, true);
        });
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // تسجيل هذه الصفحة مع مراقب المسارات
    final route = ModalRoute.of(context);
    if (route != null && route is PageRoute) {
      _routeObserver.subscribe(this, route);
    }
  }

  @override
  void dispose() {
    // إلغاء تسجيل هذه الصفحة من مراقب المسارات
    _routeObserver.unsubscribe(this);

    // تنظيف أي أحداث ماوس معلقة
    SafeMouseRegion.clearEvents();

    // إلغاء قفل أحداث الماوس في حالة كانت مقفلة
    SafeMouseRegion.unlockEvents();

    // We don't need to manually remove the listener since GetX handles this automatically
    super.dispose();
  }

  // يتم استدعاء هذه الدالة عند العودة إلى هذه الصفحة
  @override
  void didPopNext() {
    debugPrint('تم العودة إلى الصفحة الرئيسية');

    // قفل أحداث الماوس مؤقتًا عند العودة إلى الصفحة
    SafeMouseRegion.lockEvents(1000);

    // تنظيف أي أحداث ماوس معلقة
    SafeMouseRegion.clearEvents();

    // استخدام SchedulerBinding لضمان تنفيذ الكود بعد اكتمال الإطار
    SchedulerBinding.instance.addPostFrameCallback((_) {
      // إعادة تحميل المهام عند العودة إلى الصفحة مع تأخير قصير
      Future.delayed(const Duration(milliseconds: 300), () {
        reloadTasks();

        // إلغاء قفل أحداث الماوس بعد إعادة تحميل البيانات
        Future.delayed(const Duration(milliseconds: 500), () {
          SafeMouseRegion.unlockEvents();
        });
      });
    });
  }

  /// إعادة تحميل المهام حسب صلاحيات المستخدم
  Future<void> reloadTasks() async {
    debugPrint('جاري إعادة تحميل المهام...');
    final authController = Get.find<AuthController>();
    final taskController = Get.find<TaskController>();

    try {
      // تحميل المهام حسب صلاحيات المستخدم باستخدام الدالة الموحدة
      if (authController.currentUser.value != null) {
        debugPrint(
            'تحميل المهام حسب صلاحيات المستخدم: [32m${authController.currentUser.value!.id}[0m');
        await taskController
            .loadTasksByUserPermissions(authController.currentUser.value!.id, forceRefresh: true);

        // تحديث مرفقات جميع المهام من السيرفر
        for (final task in taskController.allTasks) {
          await taskController.refreshTaskAttachments(task.id);
        }

        // إشعار جميع الشاشات بأن البيانات تم تحديثها
        debugPrint('تم تحديث البيانات، إشعار الشاشات...');
      }

      // تحديث واجهة المستخدم إذا لزم الأمر
      if (mounted) {
        setState(() {});
      }
    } catch (e) {
      debugPrint('خطأ أثناء إعادة تحميل المهام: $e');
    }
  }

  Future<void> _loadInitialData() async {
    // final authController = Get.find<AuthController>(); // غير مستخدم
    // final notificationController = Get.find<NotificationController>(); // غير مستخدم
    // final taskController = Get.find<TaskController>(); // غير مستخدم

    try {
      // Load notifications if user exists
      // if (authController.currentUser.value != null) {
      //   await notificationController
      //       .loadNotifications(authController.currentUser.value!.id);
      // }

      // // تحميل المهام حسب صلاحيات المستخدم باستخدام الدالة الموحدة
      // if (authController.currentUser.value != null) {
      //   debugPrint(
      //       'تحميل المهام حسب صلاحيات المستخدم: ${authController.currentUser.value!.id}');
      //   await taskController
      //       .loadTasksByUserPermissions(authController.currentUser.value!.id);
      // } else {
      //   // Fallback - load all tasks if no user
      //   await taskController.loadAllTasks();
      // }
    } 
    catch (e) {
      // Handle errors silently to prevent app crash
      debugPrint('Error loading initial data: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Get the controllers
    final notificationController = Get.find<NotificationController>();
    final languageController = Get.find<LanguageController>();
    final themeController = Get.find<ThemeController>();
    // final notifications =Get.find<NotificationsController>();
    // notifications.refresh();

    // تم تنفيذ: تحسين التصميم المتجاوب للشاشات المختلفة (الهاتف، اللوحي، سطح المكتب)
    // تم تنفيذ: إضافة دعم للوضع الليلي (Dark Mode)
    // تم تنفيذ: تحسين تجربة المستخدم على الأجهزة ذات الشاشات الصغيرة

    // عرض شاشة تحميل إذا لم تكتمل التهيئة بعد
    if (!_isInitialized) {
      return Directionality(
        textDirection: TextDirection.rtl,
        child: Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 20),
                Text(
                  'جاري تحميل التطبيق...',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
              ],
            ),
          ),
        ),
      );
    }

    // Check if we're on a large screen (tablet or desktop)
    final isLargeScreen = ResponsiveHelper.isTablet(context) ||
        ResponsiveHelper.isDesktop(context);

    // For large screens, we'll use a different layout with a side navigation
    if (isLargeScreen) {
      return _buildDesktopLayout(
          context, notificationController, languageController, themeController);
    } else {
      // For small screens, use the original layout with bottom navigation
      return _buildMobileLayout(
          context, notificationController, languageController, themeController);
    }
  }

  // تصميم لأجهزة سطح المكتب
  Widget _buildDesktopLayout(
    BuildContext context,
    NotificationController notificationController,
    LanguageController languageController,
    ThemeController themeController,
  ) {
    // استخدام Directionality بدون Obx
    return Directionality(
      // استخدام الاتجاه من اليمين إلى اليسار للغة العربية
      textDirection:
          TextDirection.rtl, // دائمًا استخدم الاتجاه من اليمين إلى اليسار
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            _getAppBarTitle(),
            style: AppStyles.titleLarge.copyWith(
              color: AppColors.appBarText,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          elevation: 2,
          backgroundColor: AppColors.appBar,
          foregroundColor: AppColors.appBarText,
          shadowColor: AppColors.getShadowColor(0.1),
          actions: [
            // زر البحث الشامل
            if (permissionService.canAccessSearch())
              const SearchAppBarAction(),

            // زر الإشعارات
            if (permissionService.canViewNotifications())
              const NotificationsButton(),

            // زر لوحة التحكم المحسنة (حسب الصلاحيات الفعلية فقط)
            Obx(() {
              if (permissionService.canAccessEnhancedAdmin()) {
                return AppBarStyles.appBarIconButton(
                  icon: Icons.dashboard_rounded,
                  tooltip: 'لوحة التحكم المحسنة',
                  onPressed: () => Get.toNamed(AppRoutes.enhancedAdmin),
                );
              }
              return const SizedBox.shrink();
            }),

            // زر إعادة تحميل البيانات
            if (permissionService.canRefreshAdminData())
              IconButton(
                icon: Icon(
                  Icons.refresh,
                  color: AppColors.appBarIcon, // اللون الأصلي للآيكونش
                ),
                tooltip: 'إعادة تحميل البيانات',
                onPressed: _reloadData,
              ),

            // زر تبديل السمة (تغيير مباشر)
            if (permissionService.canChangeSystemTheme())
              Obx(() => AppBarStyles.appBarIconButton(
                    icon: themeController.isDarkModeRx.value
                        ? Icons.light_mode
                        : Icons.dark_mode,
                    tooltip: themeController.isDarkModeRx.value
                        ? 'الوضع الفاتح'.tr
                        : 'الوضع الداكن'.tr,
                    onPressed: () async {
                      // تبديل السمة مباشرة
                      await themeController.toggleTheme();
                      // إظهار رسالة تأكيد
                      Get.snackbar(
                        'تم تغيير السمة',
                        themeController.isDarkModeRx.value
                            ? 'تم تفعيل الوضع الداكن'
                            : 'تم تفعيل الوضع الفاتح',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: AppColors.card,
                        colorText: AppColors.textPrimary,
                        duration: const Duration(seconds: 2),
                      );
                    },
                  )),
            // زر المساعدة
            if (permissionService.canAccessHelpSystem())
              IconButton(
                icon: Icon(
                  Icons.help_outline,
                  color: AppColors.appBarIcon,
                ),
                tooltip: 'help'.tr,
                onPressed: () {
                  Get.dialog(AlertDialog(
                    backgroundColor: AppColors.card,
                    title: Text(
                      'هذه الميزة قيد التطوير',
                      style: AppStyles.titleMedium.copyWith(
                        color: AppColors.textPrimary,
                      ),
                    ),
                    content: Text(
                      'قيد التطوير',
                      style: AppStyles.bodyMedium.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Get.back(),
                        style: AppStyles.textButtonStyle,
                        child: Text(
                          'close'.tr,
                          style: AppStyles.bodyMedium.copyWith(
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ));
                },
              ),
          ],
        ),
        // استخدام الـ Drawer بدلاً من القائمة الجانبية الثابتة
        drawer: const AppDrawer(),
        backgroundColor: AppColors.background,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                AppColors.background,
                AppColors.background.withAlpha(240),
              ],
            ),
          ),
          child: _tabs[_currentIndex],
        ),
      ),
    );
  }

  // تصميم للهواتف المحمولة
  Widget _buildMobileLayout(
    BuildContext context,
    NotificationController notificationController,
    LanguageController languageController,
    ThemeController themeController,
  ) {
    // تحسين تجربة المستخدم على الأجهزة ذات الشاشات الصغيرة
    final screenWidth = ResponsiveHelper.screenWidth(context);
    final fontSize = ResponsiveHelper.getAdaptiveFontSize(context, 14);
    final iconSize = screenWidth < 360 ? 20.0 : 24.0;

    // استخدام Directionality بدون Obx
    return Directionality(
      // استخدام الاتجاه من اليمين إلى اليسار للغة العربية
      textDirection:
          TextDirection.rtl, // دائمًا استخدم الاتجاه من اليمين إلى اليسار
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            _getAppBarTitle(),
            style: AppStyles.titleLarge.copyWith(
              fontSize: fontSize,
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          centerTitle: true,
          elevation: 2,
          backgroundColor: AppColors.appBar,
          foregroundColor: AppColors.appBarText,
          shadowColor: AppColors.getShadowColor(0.1),
          actions: [
            // زر البحث الشامل
            if (permissionService.canAccessSearch())
              const SearchAppBarAction(iconSize: 20),

            // زر الإشعارات
            if (permissionService.canViewNotifications())
              const NotificationsButton(),

            // زر إعادة تحميل البيانات
            if (permissionService.canRefreshAdminData())
              AppBarStyles.appBarIconButton(
                icon: Icons.refresh,
                size: iconSize,
                tooltip: 'إعادة تحميل البيانات',
                onPressed: reloadData,
              ),

            // زر تبديل السمة (تغيير مباشر)
            if (permissionService.canChangeSystemTheme())
              Obx(() => AppBarStyles.appBarIconButton(
                    icon: themeController.isDarkModeRx.value
                        ? Icons.light_mode
                        : Icons.dark_mode,
                    size: iconSize,
                    tooltip: themeController.isDarkModeRx.value
                        ? 'الوضع الفاتح'.tr
                        : 'الوضع الداكن'.tr,
                    onPressed: () async {
                      // تبديل السمة مباشرة
                      await themeController.toggleTheme();
                      // إظهار رسالة تأكيد
                      Get.snackbar(
                        'تم تغيير السمة',
                        themeController.isDarkModeRx.value
                            ? 'تم تفعيل الوضع الداكن'
                            : 'تم تفعيل الوضع الفاتح',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: AppColors.card,
                        colorText: AppColors.textPrimary,
                        duration: const Duration(seconds: 2),
                      );
                    },
                  )),

            // زر المساعدة
            if (permissionService.canAccessHelpSystem())
              AppBarStyles.appBarIconButton(
                icon: Icons.help_outline,
                size: iconSize,
                tooltip: 'help'.tr,
                onPressed: () {
                  Get.toNamed('/help');
                },
              ),
          ],
        ),
        // استخدام الـ Drawer بدلاً من شريط التنقل السفلي
        drawer: const AppDrawer(),
        body: SafeArea(
          child: _tabs[_currentIndex],
        ),
      ),
    );
  }

  String _getAppBarTitle() {
    switch (_currentIndex) {
      case 0:
        return 'home'.tr;
      case 1:
        return 'tasks'.tr;
      case 2:
        return 'departments'.tr;
      case 3:
        return 'messages'.tr;
      case 4:
        return 'report'.tr;
      case 5:
        return 'notifications'.tr;
      case 6:
        return 'profile'.tr;
      case 7:
        return 'التقويم';
      case 8:
        return 'adminDashboard'.tr;
      case 9:
        return 'adminDashboard'.tr;
      default:
        return 'appName'.tr;
    }
  }
}
