using Microsoft.EntityFrameworkCore;
using System.Text.Json;
using webApi.Models;

namespace webApi.Services
{
    /// <summary>
    /// خدمة التسجيل الموحدة لتسجيل جميع الأحداث في النظام
    /// </summary>
    public class LoggingService : ILoggingService
    {
        private readonly TasksDbContext _context;
        private readonly ILogger<LoggingService> _logger;
        private readonly IServiceProvider _serviceProvider;

        public LoggingService(TasksDbContext context, ILogger<LoggingService> logger, IServiceProvider serviceProvider)
        {
            _context = context;
            _logger = logger;
            _serviceProvider = serviceProvider;
        }

        /// <summary>
        /// تسجيل حدث في سجل النظام
        /// </summary>
        public async Task<int> LogSystemEventAsync(
            string logType,
            string logLevel,
            string message,
            string? details = null,
            int? userId = null,
            string? ipAddress = null)
        {
            try
            {
                // استخدام DbContext منفصل لتجنب تضارب navigation properties
                using var scope = _serviceProvider.CreateScope();
                var separateContext = scope.ServiceProvider.GetRequiredService<TasksDbContext>();

                var systemLog = new SystemLog
                {
                    LogType = logType,
                    LogLevel = logLevel,
                    Message = message,
                    Details = details,
                    UserId = userId,
                    IpAddress = ipAddress,
                    CreatedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                separateContext.SystemLogs.Add(systemLog);
                await separateContext.SaveChangesAsync();

                _logger.LogInformation($"تم تسجيل حدث النظام: {logType} - {message}");
                return systemLog.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل حدث النظام: {message}");
                return 0;
            }
        }

        /// <summary>
        /// تسجيل نشاط في سجل الأنشطة
        /// </summary>
        public async Task<int> LogActivityAsync(
            string action,
            string entityType,
            int entityId,
            int userId,
            string? details = null,
            string? oldValue = null,
            string? newValue = null,
            int? taskId = null)
        {
            try
            {
                // استخدام DbContext منفصل لتجنب تضارب navigation properties
                using var scope = _serviceProvider.CreateScope();
                var separateContext = scope.ServiceProvider.GetRequiredService<TasksDbContext>();

                var activityLog = new ActivityLog
                {
                    Action = action,
                    TaskId = taskId ?? (entityType.ToLower() == "task" && entityId > 0 ? entityId : null),
                    UserId = userId,
                    Details = details,
                    ChangeType = $"{entityType}_{action}",
                    ChangeDescription = details,
                    OldValue = oldValue,
                    NewValue = newValue,
                    ChangedBy = userId,
                    Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                    ChangedAt = DateTimeOffset.UtcNow.ToUnixTimeSeconds()
                };

                separateContext.ActivityLogs.Add(activityLog);
                await separateContext.SaveChangesAsync();

                _logger.LogInformation($"تم تسجيل نشاط: {action} على {entityType} #{entityId}");
                return activityLog.Id;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل النشاط: {action} على {entityType} #{entityId}");
                return 0;
            }
        }

        /// <summary>
        /// تسجيل عملية CRUD
        /// </summary>
        public async Task<int> LogCrudOperationAsync(
            string operation,
            string entityType,
            int entityId,
            int userId,
            string? entityName = null,
            Dictionary<string, object>? changes = null,
            string? ipAddress = null)
        {
            try
            {
                // تسجيل في سجل النظام
                var systemLogMessage = $"{operation} {entityType}";
                if (!string.IsNullOrEmpty(entityName))
                    systemLogMessage += $" '{entityName}'";
                systemLogMessage += $" (ID: {entityId})";

                var systemLogDetails = changes != null ? JsonSerializer.Serialize(changes) : null;

                await LogSystemEventAsync(
                    "CRUD",
                    "INFO",
                    systemLogMessage,
                    systemLogDetails,
                    userId,
                    ipAddress);

                // تسجيل في سجل الأنشطة
                var activityDetails = $"{GetArabicOperation(operation)} {GetArabicEntityType(entityType)}";
                if (!string.IsNullOrEmpty(entityName))
                    activityDetails += $" '{entityName}'";

                string? oldValue = null;
                string? newValue = null;

                if (changes != null && changes.Count > 0)
                {
                    if (operation == "UPDATE")
                    {
                        var oldValues = changes.Where(c => c.Key.StartsWith("old_")).ToDictionary(c => c.Key.Substring(4), c => c.Value);
                        var newValues = changes.Where(c => c.Key.StartsWith("new_")).ToDictionary(c => c.Key.Substring(4), c => c.Value);

                        if (oldValues.Count > 0) oldValue = JsonSerializer.Serialize(oldValues);
                        if (newValues.Count > 0) newValue = JsonSerializer.Serialize(newValues);
                    }
                    else if (operation == "CREATE")
                    {
                        newValue = JsonSerializer.Serialize(changes);
                    }
                }

                return await LogActivityAsync(
                    operation.ToLower(),
                    entityType,
                    entityId,
                    userId,
                    activityDetails,
                    oldValue,
                    newValue);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل عملية CRUD: {operation} {entityType} #{entityId}");
                return 0;
            }
        }

        /// <summary>
        /// تسجيل حدث أمني
        /// </summary>
        public async Task<int> LogSecurityEventAsync(
            string securityEvent,
            string message,
            int? userId = null,
            string? ipAddress = null,
            string? details = null)
        {
            try
            {
                // تسجيل في سجل النظام
                await LogSystemEventAsync(
                    "SECURITY",
                    "WARNING",
                    $"حدث أمني: {securityEvent} - {message}",
                    details,
                    userId,
                    ipAddress);

                // تسجيل في سجل الأنشطة إذا كان هناك مستخدم
                if (userId.HasValue)
                {
                    return await LogActivityAsync(
                        "security_event",
                        "security",
                        0,
                        userId.Value,
                        $"حدث أمني: {securityEvent} - {message}");
                }

                return 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل الحدث الأمني: {securityEvent}");
                return 0;
            }
        }

        /// <summary>
        /// تسجيل خطأ في النظام
        /// </summary>
        public async Task<int> LogErrorAsync(
            Exception exception,
            string context,
            int? userId = null,
            string? ipAddress = null)
        {
            try
            {
                var errorDetails = new
                {
                    ExceptionType = exception.GetType().Name,
                    Message = exception.Message,
                    StackTrace = exception.StackTrace,
                    InnerException = exception.InnerException?.Message,
                    Context = context
                };

                return await LogSystemEventAsync(
                    "ERROR",
                    "ERROR",
                    $"خطأ في {context}: {exception.Message}",
                    JsonSerializer.Serialize(errorDetails),
                    userId,
                    ipAddress);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل الخطأ: {context}");
                return 0;
            }
        }

        /// <summary>
        /// تسجيل تسجيل دخول/خروج
        /// </summary>
        public async Task<int> LogAuthenticationAsync(
            string action,
            int? userId,
            string username,
            string? ipAddress = null,
            string? userAgent = null,
            bool success = true)
        {
            try
            {
                var message = action == "login" ? "تسجيل دخول" : "تسجيل خروج";
                if (!success) message += " فاشل";

                var details = new
                {
                    Username = username,
                    UserAgent = userAgent,
                    Success = success,
                    Timestamp = DateTime.UtcNow
                };

                // تسجيل في سجل النظام
                await LogSystemEventAsync(
                    "AUTHENTICATION",
                    success ? "INFO" : "WARNING",
                    $"{message} للمستخدم {username}",
                    JsonSerializer.Serialize(details),
                    userId,
                    ipAddress);

                // تسجيل في سجل الأنشطة إذا نجح وكان هناك معرف مستخدم
                if (success && userId.HasValue)
                {
                    return await LogActivityAsync(
                        action,
                        "user",
                        userId.Value,
                        userId.Value,
                        $"{message} بنجاح");
                }

                return 1;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"خطأ في تسجيل المصادقة: {action} للمستخدم {username}");
                return 0;
            }
        }

        /// <summary>
        /// ترجمة نوع العملية إلى العربية
        /// </summary>
        private static string GetArabicOperation(string operation)
        {
            return operation.ToUpper() switch
            {
                "CREATE" => "إنشاء",
                "UPDATE" => "تحديث",
                "DELETE" => "حذف",
                "READ" => "عرض",
                _ => operation
            };
        }

        /// <summary>
        /// ترجمة نوع الكيان إلى العربية
        /// </summary>
        private static string GetArabicEntityType(string entityType)
        {
            return entityType.ToLower() switch
            {
                "task" => "مهمة",
                "user" => "مستخدم",
                "department" => "قسم",
                "role" => "دور",
                "permission" => "صلاحية",
                "message" => "رسالة",
                "attachment" => "مرفق",
                "comment" => "تعليق",
                "document" => "مستند",
                _ => entityType
            };
        }
    }
}
