{"Version": 1, "Hash": "DkUjuUah7OOFfy6TVjGLRtgifhC1hGVbsGsEK6pvATY=", "Source": "webApi", "BasePath": "_content/webApi", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "webApi\\wwwroot", "Source": "webApi", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "Pattern": "**"}], "Assets": [{"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd#[.{fingerprint=je3vbe9eb6}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "qva2e0zyb4", "Integrity": "at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "FileLength": 5246, "LastWriteTime": "2025-07-28T23:42:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lcfl19kylj", "Integrity": "C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "FileLength": 756, "LastWriteTime": "2025-07-28T23:42:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76#[.{fingerprint=ecem5x2v5n}]?.csv.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "lcfl19kylj", "Integrity": "C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "FileLength": 756, "LastWriteTime": "2025-07-28T23:42:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 1927, "LastWriteTime": "2025-07-28T23:42:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint=knc7nr4hqg}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "72uezgg7hx", "Integrity": "UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 1927, "LastWriteTime": "2025-07-28T23:42:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint=dz1rjo<PERSON>cz<PERSON>}]?.txt.gz", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Alternative", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetTraitName": "Content-Encoding", "AssetTraitValue": "gzip", "Fingerprint": "00icdbvmee", "Integrity": "II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 7549, "LastWriteTime": "2025-07-28T23:42:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250707_153306_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250707_153306_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3nitcostfa", "Integrity": "YRBe6ysvjXyhkPDFQzZZqYRiwyPmqlD5uakf7sPyy/8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250707_153306_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-07T15:33:06+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250712_033808_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250712_033808_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "18we1jq8q4", "Integrity": "oQeieJeQARhNT+LAEDSPxfV6ph1EDZW9Mc0jps4OIjk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250712_033808_0.sql", "FileLength": 35742208, "LastWriteTime": "2025-07-12T03:38:08+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_141356_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_141356_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wei1ctf611", "Integrity": "iFx6term7Iii94BvNRILue9lfdAxsIF4l2QmqWMRkx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_141356_0.sql", "FileLength": 64536064, "LastWriteTime": "2025-07-15T14:44:00+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144541_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_144541_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u0kw9kgy4w", "Integrity": "otlzc+n4ZtXnuOb3H8oOLLeUzfrsLht7Fbj6JYZa7Vo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_144541_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T14:45:41+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144735_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_144735_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "olba8xqros", "Integrity": "POXDw4nr2OA8/3aowe2zlxCrBs7mXmm+YfksbHdGlbk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_144735_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T14:47:35+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150009_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_150009_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zy0q7wnq3f", "Integrity": "a152NpsfczkDHXjEOwKc4ScwQ/u8Waryi4+07uGzY3U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_150009_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:00:09+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150334_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_150334_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jwjdiclzr9", "Integrity": "A7iUtACQkR9+3W8ck0fZPDLcJejcMLHZ7NlDvxMuJx0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_150334_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:03:34+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151225_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_151225_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d77ue2n4io", "Integrity": "B/qYvn8Pf2u6auT+G2yh7pkwhujz/k7yZAEYwGTKpfY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_151225_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:12:25+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151229_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_151229_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mq6ep6mvf6", "Integrity": "YjU5e2A+IpX/NzDdAy8M8RWuFn+/HBToRTFV8KAfuJs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_151229_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:12:29+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153548_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_153548_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hxng2sfg3k", "Integrity": "Jf+h2r8SstAga8I8YNaDLIDabcFU/4P559RR/vHiViY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_153548_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:35:48+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153556_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_153556_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wyz7qo4ryg", "Integrity": "2GseJ5dWr/4A3VO2DLXr8mtyw9grW7EaLReEjDNaJMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_153556_0.sql", "FileLength": 32072192, "LastWriteTime": "2025-07-15T15:35:56+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153621_0.sql", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "backups/backup_20250715_153621_0#[.{fingerprint}]?.sql", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yosmphs06q", "Integrity": "PmiGQrx/QjOK9CoJe94vh7pezoZD3G+AZI6tCh4lLqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\backups\\backup_20250715_153621_0.sql", "FileLength": 107354624, "LastWriteTime": "2025-07-20T19:50:18+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\archives\\be70474c4b4744bc9edf86d5b08c891e.rar", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/archives/be70474c4b4744bc9edf86d5b08c891e#[.{fingerprint}]?.rar", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6j92<PERSON><PERSON><PERSON><PERSON>", "Integrity": "Pl7DPbWsz7LGbqR3M1VbuZkvPowr6LTDhbz24fXxpag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\archives\\be70474c4b4744bc9edf86d5b08c891e.rar", "FileLength": 16312166, "LastWriteTime": "2025-07-23T17:42:09+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "FileLength": 97050, "LastWriteTime": "2025-06-22T15:42:44+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "FileLength": 1607, "LastWriteTime": "2025-06-20T19:14:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q5n9oh9rw1", "Integrity": "gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "FileLength": 1091955, "LastWriteTime": "2025-06-20T21:20:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2)#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dz1rjozczi", "Integrity": "NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "FileLength": 28469, "LastWriteTime": "2025-06-20T19:16:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "je3vbe9eb6", "Integrity": "+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "FileLength": 24217, "LastWriteTime": "2025-07-23T21:56:34+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ecem5x2v5n", "Integrity": "4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "FileLength": 3535, "LastWriteTime": "2025-07-19T19:08:43+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76#[.{fingerprint}]?.csv", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ecem5x2v5n", "Integrity": "4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "FileLength": 3535, "LastWriteTime": "2025-07-19T19:11:14+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\02cc06431c17416889e85b70a05142e2.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/02cc06431c17416889e85b70a05142e2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\02cc06431c17416889e85b70a05142e2.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:20:30+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\052eb05336464289832f7814ce2d7630.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/052eb05336464289832f7814ce2d7630#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\052eb05336464289832f7814ce2d7630.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-11T22:16:28+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0717d4b8b05f4122a2163034c59e5f22.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/0717d4b8b05f4122a2163034c59e5f22#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gonabiiekc", "Integrity": "YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\0717d4b8b05f4122a2163034c59e5f22.pdf", "FileLength": 91721, "LastWriteTime": "2025-07-11T23:41:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0dfd9a3304db455a859caab855e678f4.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/0dfd9a3304db455a859caab855e678f4#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q5n9oh9rw1", "Integrity": "gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\0dfd9a3304db455a859caab855e678f4.pdf", "FileLength": 1091955, "LastWriteTime": "2025-07-12T01:56:12+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\18e70463ba004856925a8d0e72b2b242.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/18e70463ba004856925a8d0e72b2b242#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e1igdv5m66", "Integrity": "UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\18e70463ba004856925a8d0e72b2b242.pdf", "FileLength": 2154, "LastWriteTime": "2025-07-17T02:16:16+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\1d66653fc02145d3a7be0572160c3eb0.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/1d66653fc02145d3a7be0572160c3eb0#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kpn9enu225", "Integrity": "SMpop/jX5zRJnJyEOe34+XpqryRJZKVLM6GLh7a0EJM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\1d66653fc02145d3a7be0572160c3eb0.pdf", "FileLength": 1303771, "LastWriteTime": "2025-07-11T21:41:19+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "FileLength": 1607, "LastWriteTime": "2025-07-03T01:41:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "knc7nr4hqg", "Integrity": "MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "FileLength": 7630, "LastWriteTime": "2025-06-24T14:41:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3765e865484c4ea39fc07eb5a9182354.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/3765e865484c4ea39fc07eb5a9182354#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\3765e865484c4ea39fc07eb5a9182354.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:24:28+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\37b012f3d1584ff38e3dd454fe705980.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/37b012f3d1584ff38e3dd454fe705980#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\37b012f3d1584ff38e3dd454fe705980.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-08T15:05:30+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/3982df413cf14caf9da852e86398c3f2#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-03T17:35:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\429aa494b4f6454aa6a35873f7c8401f.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/429aa494b4f6454aa6a35873f7c8401f#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mqlqasmzgb", "Integrity": "crWI8ObgLXTKW55S4HeY98kcaiPSPgifDt/PhRrL8lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\429aa494b4f6454aa6a35873f7c8401f.pdf", "FileLength": 441546, "LastWriteTime": "2025-07-23T16:01:30+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\458bc2d8a56c42e4aca145ca1cdbf135.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/458bc2d8a56c42e4aca145ca1cdbf135#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "et9dbzk0w9", "Integrity": "DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\458bc2d8a56c42e4aca145ca1cdbf135.pdf", "FileLength": 25855, "LastWriteTime": "2025-07-13T03:26:20+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\48082c73ee21481e8b2b10f9300d66bc.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/48082c73ee21481e8b2b10f9300d66bc#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3cceavjbna", "Integrity": "80su5ua6RMNKr09mBcSYxyWu6S68zXgD4tniX2F372k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\48082c73ee21481e8b2b10f9300d66bc.pdf", "FileLength": 1226126, "LastWriteTime": "2025-07-17T01:07:50+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/57cc983c754e400fa8630e31eae1bc49#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-03T01:09:13+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\597d0e98415a4c19b8d7267d6e4c096a.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/597d0e98415a4c19b8d7267d6e4c096a#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\597d0e98415a4c19b8d7267d6e4c096a.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:27:45+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\5b9afba8483c42c688d10268e402bf63.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/5b9afba8483c42c688d10268e402bf63#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gonabiiekc", "Integrity": "YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\5b9afba8483c42c688d10268e402bf63.pdf", "FileLength": 91721, "LastWriteTime": "2025-07-11T21:05:07+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\6d49532932024faf81b6cc20c91ba142.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/6d49532932024faf81b6cc20c91ba142#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\6d49532932024faf81b6cc20c91ba142.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:24:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\7091b5e0eb5f41929191868a1ccdb942.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/7091b5e0eb5f41929191868a1ccdb942#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\7091b5e0eb5f41929191868a1ccdb942.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-11T23:45:56+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6mabyag1xn", "Integrity": "gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "FileLength": 48194, "LastWriteTime": "2025-06-23T16:05:03+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8375e14b17a341efb982c00a4344db2c#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "FileLength": 2149873, "LastWriteTime": "2025-07-03T01:46:36+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\838f2c3b1144437183c209880c5ddf08.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/838f2c3b1144437183c209880c5ddf08#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lnw1de61i1", "Integrity": "kSBidZdGBlsfJZ3Kas8ksc+ppgV93hDK66H8YS77dus=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\838f2c3b1144437183c209880c5ddf08.pdf", "FileLength": 7245, "LastWriteTime": "2025-07-17T01:41:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\84b9165f75994121b64d6ab3a05d38fd.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/84b9165f75994121b64d6ab3a05d38fd#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\84b9165f75994121b64d6ab3a05d38fd.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-17T03:14:57+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "knc7nr4hqg", "Integrity": "MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "FileLength": 7630, "LastWriteTime": "2025-06-23T16:18:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a24bf33b26f34799b3d0330291adf0fc.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/a24bf33b26f34799b3d0330291adf0fc#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yqs7wwjalp", "Integrity": "St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\a24bf33b26f34799b3d0330291adf0fc.pdf", "FileLength": 515576, "LastWriteTime": "2025-07-17T02:38:09+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a91fd552aae140a186b9f82e91f6d071.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/a91fd552aae140a186b9f82e91f6d071#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cgulgik6pq", "Integrity": "lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\a91fd552aae140a186b9f82e91f6d071.pdf", "FileLength": 1303767, "LastWriteTime": "2025-07-11T21:22:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pbhq5urrew", "Integrity": "Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "FileLength": 1607, "LastWriteTime": "2025-06-23T15:48:23+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/bff0abcb17524585a06eee3255b1f49a#[.{fingerprint}]?.docx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "v8f3tesalf", "Integrity": "7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "FileLength": 2149873, "LastWriteTime": "2025-06-29T00:48:53+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\c3d68bb6815944c38541f95cdc697682.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/c3d68bb6815944c38541f95cdc697682#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nmzbkjlql8", "Integrity": "4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\c3d68bb6815944c38541f95cdc697682.pdf", "FileLength": 23575, "LastWriteTime": "2025-07-17T03:34:44+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\cb01a1c7ff704836b8a4575cebba0f7d.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/cb01a1c7ff704836b8a4575cebba0f7d#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nmzbkjlql8", "Integrity": "4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\cb01a1c7ff704836b8a4575cebba0f7d.pdf", "FileLength": 23575, "LastWriteTime": "2025-07-17T17:26:45+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o5mrvqq7ms", "Integrity": "7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "FileLength": 147799, "LastWriteTime": "2025-06-29T00:59:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\db26e8581e504a6fbe89c9f2c25ea049.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/db26e8581e504a6fbe89c9f2c25ea049#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e1igdv5m66", "Integrity": "UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\db26e8581e504a6fbe89c9f2c25ea049.pdf", "FileLength": 2154, "LastWriteTime": "2025-07-13T02:30:08+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6mabyag1xn", "Integrity": "gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "FileLength": 48194, "LastWriteTime": "2025-06-23T18:32:56+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea02708c3aca4feca7c6fddf23666bfe.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/ea02708c3aca4feca7c6fddf23666bfe#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "et9dbzk0w9", "Integrity": "DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\ea02708c3aca4feca7c6fddf23666bfe.pdf", "FileLength": 25855, "LastWriteTime": "2025-07-17T01:07:53+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea1632403571458e8fd9910c56d0f0f5.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/ea1632403571458e8fd9910c56d0f0f5#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kob432tvll", "Integrity": "JM+8CsTmG0kHcdIAFCZqjv59Fki8RHEaHqDxVI87feA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\ea1632403571458e8fd9910c56d0f0f5.pdf", "FileLength": 301140, "LastWriteTime": "2025-07-08T03:27:31+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\eb65cd3797854633828ab9b66feaa963.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/eb65cd3797854633828ab9b66feaa963#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gonabiiekc", "Integrity": "YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\eb65cd3797854633828ab9b66feaa963.pdf", "FileLength": 91721, "LastWriteTime": "2025-07-11T22:34:48+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/f3aa1eeb1a4a43f39ee2622a9c538fff#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "et9dbzk0w9", "Integrity": "DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "FileLength": 25855, "LastWriteTime": "2025-07-13T02:49:03+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f5a02559328b46e78d0360bce78e29a6.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/f5a02559328b46e78d0360bce78e29a6#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "e1igdv5m66", "Integrity": "UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\f5a02559328b46e78d0360bce78e29a6.pdf", "FileLength": 2154, "LastWriteTime": "2025-07-12T22:22:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012#[.{fingerprint}]?.pdf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jilall62b3", "Integrity": "axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "FileLength": 63019, "LastWriteTime": "2025-06-24T14:04:39+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\012f27a40a4b47e6ae5db437acc75f99.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/012f27a40a4b47e6ae5db437acc75f99#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bm200vq19x", "Integrity": "Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\012f27a40a4b47e6ae5db437acc75f99.png", "FileLength": 51973, "LastWriteTime": "2025-07-28T19:06:45+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\058264df89934078a8fc82a563396495.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/058264df89934078a8fc82a563396495#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\058264df89934078a8fc82a563396495.png", "FileLength": 196383, "LastWriteTime": "2025-07-11T23:54:44+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\0927b9a8ae0b4ab991bd710ade619ee2.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/0927b9a8ae0b4ab991bd710ade619ee2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\0927b9a8ae0b4ab991bd710ade619ee2.png", "FileLength": 195627, "LastWriteTime": "2025-07-23T22:16:17+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\09adcd0d2d104d32ac9533b83026a8d1.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/09adcd0d2d104d32ac9533b83026a8d1#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\09adcd0d2d104d32ac9533b83026a8d1.png", "FileLength": 196383, "LastWriteTime": "2025-07-09T21:10:49+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\10052d090a244fe28441690bd549cf4f.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/10052d090a244fe28441690bd549cf4f#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\10052d090a244fe28441690bd549cf4f.webp", "FileLength": 97050, "LastWriteTime": "2025-07-11T21:49:32+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\158c4d49e0444e039ee77f372cb74346.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/158c4d49e0444e039ee77f372cb74346#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\158c4d49e0444e039ee77f372cb74346.png", "FileLength": 726022, "LastWriteTime": "2025-07-11T21:42:20+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\16cbcd9c40c848068b1303ef10915a19.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/16cbcd9c40c848068b1303ef10915a19#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "npx0pid61y", "Integrity": "qztz0HhubvxF0ZyaTYkeNdm1yfNPP85GMBMjocWues0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\16cbcd9c40c848068b1303ef10915a19.png", "FileLength": 890, "LastWriteTime": "2025-07-13T02:43:16+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/1870288851874090b203bf27c9321fed#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "FileLength": 885730, "LastWriteTime": "2025-06-24T13:55:30+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\27cdd4e56358437d960e875392a5c2b1.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/27cdd4e56358437d960e875392a5c2b1#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\27cdd4e56358437d960e875392a5c2b1.webp", "FileLength": 97050, "LastWriteTime": "2025-07-08T15:19:37+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/312205ccd18f4650bef24653fbf9f64a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "FileLength": 726022, "LastWriteTime": "2025-06-24T14:45:00+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314d69b11b25437fa3e677cb0bc0df70.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/314d69b11b25437fa3e677cb0bc0df70#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\314d69b11b25437fa3e677cb0bc0df70.png", "FileLength": 180218, "LastWriteTime": "2025-07-13T02:42:50+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314ee81838e24ff69f9376e5ad555018.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/314ee81838e24ff69f9376e5ad555018#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\314ee81838e24ff69f9376e5ad555018.png", "FileLength": 195627, "LastWriteTime": "2025-07-17T16:03:31+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\338974a63b324a6f8e140174a2be9358.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/338974a63b324a6f8e140174a2be9358#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\338974a63b324a6f8e140174a2be9358.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T15:32:06+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34222e73c2af41da84d4996eb1794010.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/34222e73c2af41da84d4996eb1794010#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\34222e73c2af41da84d4996eb1794010.png", "FileLength": 195627, "LastWriteTime": "2025-07-11T23:47:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34a889f3e0ce40e085d25f74812e870b.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/34a889f3e0ce40e085d25f74812e870b#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\34a889f3e0ce40e085d25f74812e870b.png", "FileLength": 726022, "LastWriteTime": "2025-07-08T03:37:20+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\383b2c9c091843a698a7f649130db384.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/383b2c9c091843a698a7f649130db384#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c40i8h3quo", "Integrity": "HG0sEXWwXhhOpCV7Bk43ZIow61iO8OfJhFf0De0lYvc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\383b2c9c091843a698a7f649130db384.png", "FileLength": 70779, "LastWriteTime": "2025-07-27T02:42:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj02sd2q5h", "Integrity": "wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "FileLength": 1280957, "LastWriteTime": "2025-06-23T17:18:40+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\43ff42ee0f494b1fac40397ff6cb35e4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/43ff42ee0f494b1fac40397ff6cb35e4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\43ff42ee0f494b1fac40397ff6cb35e4.png", "FileLength": 195627, "LastWriteTime": "2025-07-13T03:32:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qt5iiuu92c", "Integrity": "/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "FileLength": 14596, "LastWriteTime": "2025-06-23T15:16:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\48d3116a39f34661b105e97351e9dd65.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/48d3116a39f34661b105e97351e9dd65#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\48d3116a39f34661b105e97351e9dd65.png", "FileLength": 86139, "LastWriteTime": "2025-07-17T01:23:53+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "160dqoac5z", "Integrity": "52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "FileLength": 853270, "LastWriteTime": "2025-06-23T17:13:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4ca45322624947dfa4c15343a522edaa#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T17:05:46+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4fa649b91a96437e8b2da7b488ff9c72.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/4fa649b91a96437e8b2da7b488ff9c72#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\4fa649b91a96437e8b2da7b488ff9c72.png", "FileLength": 196383, "LastWriteTime": "2025-07-20T15:37:10+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5094f812e6634ab4acc07ed00767abd5.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5094f812e6634ab4acc07ed00767abd5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qm5cpjs4gn", "Integrity": "gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5094f812e6634ab4acc07ed00767abd5.png", "FileLength": 32278, "LastWriteTime": "2025-07-16T21:21:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/53e82cd4346e4263bc415d38312d7b49#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "csvf1bo9y0", "Integrity": "VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "FileLength": 158471, "LastWriteTime": "2025-06-24T14:43:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59d9b375bfc749ee94270d0724e0d922.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/59d9b375bfc749ee94270d0724e0d922#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\59d9b375bfc749ee94270d0724e0d922.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T17:14:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59e3e01493f748bfb81c21759fbe6502.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/59e3e01493f748bfb81c21759fbe6502#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mi7mdsl38n", "Integrity": "cgPuaqI2ICuLaq72Z4vc717DOoL+YLIVdE6EK57AIMQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\59e3e01493f748bfb81c21759fbe6502.png", "FileLength": 542436, "LastWriteTime": "2025-07-28T18:27:20+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f111548ca9e4c5cb05845290f0b0eb5.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f111548ca9e4c5cb05845290f0b0eb5#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f111548ca9e4c5cb05845290f0b0eb5.webp", "FileLength": 97050, "LastWriteTime": "2025-07-11T23:51:50+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f82d034f5fc408991373eec12a04772.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f82d034f5fc408991373eec12a04772#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f82d034f5fc408991373eec12a04772.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-13T04:23:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f8765d1b6124a7c95a2923a03bbe7ab.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f8765d1b6124a7c95a2923a03bbe7ab#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f8765d1b6124a7c95a2923a03bbe7ab.png", "FileLength": 180218, "LastWriteTime": "2025-07-12T00:43:40+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f98bb1a22fc476f974000194d3ee208.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/5f98bb1a22fc476f974000194d3ee208#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\5f98bb1a22fc476f974000194d3ee208.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T23:01:48+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "FileLength": 97050, "LastWriteTime": "2025-06-29T01:16:18+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\621c79053943489d9838496dccd18b32.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/621c79053943489d9838496dccd18b32#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\621c79053943489d9838496dccd18b32.png", "FileLength": 196383, "LastWriteTime": "2025-07-23T22:11:33+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "FileLength": 885730, "LastWriteTime": "2025-06-24T15:23:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\66ca76d3dec94b7fa33213be976243b2.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/66ca76d3dec94b7fa33213be976243b2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\66ca76d3dec94b7fa33213be976243b2.png", "FileLength": 86139, "LastWriteTime": "2025-07-11T23:54:35+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\69b88efe4c09475aba9ea7025d4e346f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/69b88efe4c09475aba9ea7025d4e346f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\69b88efe4c09475aba9ea7025d4e346f.png", "FileLength": 726022, "LastWriteTime": "2025-07-11T23:48:14+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6a008f012f2b46a9a77ddbed720921cf.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6a008f012f2b46a9a77ddbed720921cf#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6a008f012f2b46a9a77ddbed720921cf.png", "FileLength": 195627, "LastWriteTime": "2025-07-23T21:57:08+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "FileLength": 1011297, "LastWriteTime": "2025-06-23T14:50:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d8d0634bb084b3380094f266ed72df7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/6d8d0634bb084b3380094f266ed72df7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\6d8d0634bb084b3380094f266ed72df7.png", "FileLength": 86139, "LastWriteTime": "2025-07-11T22:30:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1nfn82ehfv", "Integrity": "E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "FileLength": 1345158, "LastWriteTime": "2025-06-24T14:30:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/713c55958e434619b8dbd17a5ef62b7e#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "FileLength": 885730, "LastWriteTime": "2025-06-23T16:21:07+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\73ef279aa84b42e1a830b0c1747a9baa.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/73ef279aa84b42e1a830b0c1747a9baa#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\73ef279aa84b42e1a830b0c1747a9baa.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T03:11:00+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\757730977ffe43dbbdef2c17e557a5cb.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/757730977ffe43dbbdef2c17e557a5cb#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gs16logzrz", "Integrity": "kKNYPA2xO19s3COMjoENuFzDJDboPkVRPBOvK78B+lA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\757730977ffe43dbbdef2c17e557a5cb.png", "FileLength": 65595, "LastWriteTime": "2025-07-26T19:17:41+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\771992cb22384d97bd1f5d0718a5bac6.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/771992cb22384d97bd1f5d0718a5bac6#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\771992cb22384d97bd1f5d0718a5bac6.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T19:03:03+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\783de52104b84aac9fff70eb4857a083.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/783de52104b84aac9fff70eb4857a083#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\783de52104b84aac9fff70eb4857a083.webp", "FileLength": 97050, "LastWriteTime": "2025-07-13T03:32:07+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8422115944564f0d9b5336c152d37758#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "FileLength": 196383, "LastWriteTime": "2025-06-23T15:04:29+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\866b5614a13843f5a8c082332a886e8d.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/866b5614a13843f5a8c082332a886e8d#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\866b5614a13843f5a8c082332a886e8d.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T19:04:27+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/86f9084a89de49dcb333d63c0bf993b9#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3oz33zfofh", "Integrity": "UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "FileLength": 87614, "LastWriteTime": "2025-06-23T16:15:05+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/87959591ff1d4bb9b6d3cf9617456555#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "45mp2a8mvz", "Integrity": "qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "FileLength": 659876, "LastWriteTime": "2025-06-24T14:16:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8891458a31864b6a92594c256956cc0f.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8891458a31864b6a92594c256956cc0f#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8891458a31864b6a92594c256956cc0f.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T23:01:25+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "FileLength": 726022, "LastWriteTime": "2025-06-24T14:36:28+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e75782283de4a1ab7da0396404ac48c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8e75782283de4a1ab7da0396404ac48c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8e75782283de4a1ab7da0396404ac48c.png", "FileLength": 196383, "LastWriteTime": "2025-07-11T21:58:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8f38d922b2a6453e8d9df2beb9012c53.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/8f38d922b2a6453e8d9df2beb9012c53#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\8f38d922b2a6453e8d9df2beb9012c53.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T23:01:12+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\90fda8458bb9455ea6069717e3093d03.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/90fda8458bb9455ea6069717e3093d03#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\90fda8458bb9455ea6069717e3093d03.webp", "FileLength": 97050, "LastWriteTime": "2025-07-11T23:42:47+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9130a0bd7c4b4a3599d408a4fe24745a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9130a0bd7c4b4a3599d408a4fe24745a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9130a0bd7c4b4a3599d408a4fe24745a.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T21:11:58+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/97b49fcfbb844432a4b311cb199e86f9#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "FileLength": 86139, "LastWriteTime": "2025-07-03T01:54:38+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9854cf13659b4bb6a2141d0824af4666#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "FileLength": 86139, "LastWriteTime": "2025-06-23T15:07:12+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\98a77b5c42654bf6b3f376aa6860eed5.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/98a77b5c42654bf6b3f376aa6860eed5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\98a77b5c42654bf6b3f376aa6860eed5.png", "FileLength": 180218, "LastWriteTime": "2025-07-12T00:47:50+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9a342b7e0e404095a917be1fc40364ae#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T02:50:41+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a7cecf2a2af4dc88583fc5a439133cf.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9a7cecf2a2af4dc88583fc5a439133cf#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9a7cecf2a2af4dc88583fc5a439133cf.webp", "FileLength": 97050, "LastWriteTime": "2025-07-09T22:28:29+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9ac777271fb344708f6133a9f76a4bf5.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9ac777271fb344708f6133a9f76a4bf5#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9ac777271fb344708f6133a9f76a4bf5.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T19:05:12+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9c71ea3b963b479e9df40428b62504be.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9c71ea3b963b479e9df40428b62504be#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9c71ea3b963b479e9df40428b62504be.png", "FileLength": 216207, "LastWriteTime": "2025-07-28T18:33:05+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r7rmd0638a", "Integrity": "uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "FileLength": 155911, "LastWriteTime": "2025-06-23T16:15:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/a64a54a1298e4b8c81bf46d705996980#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "160dqoac5z", "Integrity": "52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "FileLength": 853270, "LastWriteTime": "2025-06-24T13:31:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aa469b655153465e9cd1265d5a36a8dd#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T02:41:59+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aa812b7960134dd380c2c5fcc1286289#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T18:31:04+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ae1bc4df202d4ee7ad0662c86c040945.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ae1bc4df202d4ee7ad0662c86c040945#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qm5cpjs4gn", "Integrity": "gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ae1bc4df202d4ee7ad0662c86c040945.png", "FileLength": 32278, "LastWriteTime": "2025-07-13T02:48:17+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/aee04329d556487dbb70eccf248638db#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "FileLength": 196383, "LastWriteTime": "2025-07-03T01:48:56+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3ac325587274e74a2f539ae88f27bf0.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/b3ac325587274e74a2f539ae88f27bf0#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\b3ac325587274e74a2f539ae88f27bf0.webp", "FileLength": 97050, "LastWriteTime": "2025-07-23T22:08:28+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3c8c8f821c4448f958edb509d8ca866.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/b3c8c8f821c4448f958edb509d8ca866#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\b3c8c8f821c4448f958edb509d8ca866.png", "FileLength": 216207, "LastWriteTime": "2025-07-28T19:07:26+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b54042592d954bf29ca490c559a2c12b.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/b54042592d954bf29ca490c559a2c12b#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\b54042592d954bf29ca490c559a2c12b.png", "FileLength": 726022, "LastWriteTime": "2025-07-17T14:48:20+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/bd28a12d15e24fb48511eb051459a6bc#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ia1swkvltk", "Integrity": "duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "FileLength": 885730, "LastWriteTime": "2025-06-23T16:07:29+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lb3ihrmur6", "Integrity": "r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "FileLength": 62981, "LastWriteTime": "2025-06-23T15:16:53+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c2e7dc6ac9f743a4b5443b55463b3766.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c2e7dc6ac9f743a4b5443b55463b3766#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sejuu0i0l0", "Integrity": "PERl6HpxIJLpQ9Lch2j8XLS2/Y4kw8lhv7j40ENBi2I=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c2e7dc6ac9f743a4b5443b55463b3766.png", "FileLength": 66359, "LastWriteTime": "2025-07-28T19:02:36+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ljkl4o8aee", "Integrity": "v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "FileLength": 3582183, "LastWriteTime": "2025-06-23T18:33:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "FileLength": 97050, "LastWriteTime": "2025-06-23T02:38:51+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c835a2f77dc6491195963a76f091b3d8.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/c835a2f77dc6491195963a76f091b3d8#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\c835a2f77dc6491195963a76f091b3d8.webp", "FileLength": 97050, "LastWriteTime": "2025-07-18T22:21:49+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ca0161389fa346e6be985efb101cf3f7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "45mp2a8mvz", "Integrity": "qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "FileLength": 659876, "LastWriteTime": "2025-06-24T15:28:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d0f43a0c17dd4ae78ab837ce42692072.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d0f43a0c17dd4ae78ab837ce42692072#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d0f43a0c17dd4ae78ab837ce42692072.png", "FileLength": 216207, "LastWriteTime": "2025-07-28T21:12:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d277b7275c644e738414f5c2bc40bc99.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d277b7275c644e738414f5c2bc40bc99#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d277b7275c644e738414f5c2bc40bc99.png", "FileLength": 195627, "LastWriteTime": "2025-07-08T03:34:52+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d36bc059c16b44bcbc0a35050a0a0b5e#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vcjj9guxre", "Integrity": "v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "FileLength": 46057, "LastWriteTime": "2025-07-16T21:21:32+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5ef1ef2d081452180eec81c36b2b8e9.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d5ef1ef2d081452180eec81c36b2b8e9#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bhip2kqmjj", "Integrity": "z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d5ef1ef2d081452180eec81c36b2b8e9.png", "FileLength": 364136, "LastWriteTime": "2025-07-28T19:03:55+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5effe60e3e84110858bb6b1629056c4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d5effe60e3e84110858bb6b1629056c4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d5effe60e3e84110858bb6b1629056c4.png", "FileLength": 180218, "LastWriteTime": "2025-07-11T23:57:37+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d9e709f5e5be4ee4afcff29995499f15.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/d9e709f5e5be4ee4afcff29995499f15#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\d9e709f5e5be4ee4afcff29995499f15.png", "FileLength": 180218, "LastWriteTime": "2025-07-13T02:41:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/db5e4060478d44bdbd0eda3592b741c4#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "FileLength": 195627, "LastWriteTime": "2025-06-29T01:16:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\dd0c100c1a6e43efbee4ea0708b5eefa.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/dd0c100c1a6e43efbee4ea0708b5eefa#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "evylwqip3e", "Integrity": "NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\dd0c100c1a6e43efbee4ea0708b5eefa.png", "FileLength": 86139, "LastWriteTime": "2025-07-27T02:42:45+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\de20a39284b94cc0b97bb816eb1ac085.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/de20a39284b94cc0b97bb816eb1ac085#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\de20a39284b94cc0b97bb816eb1ac085.png", "FileLength": 195627, "LastWriteTime": "2025-07-17T02:34:23+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\deef804fae0c4fa08e7b1b901925419a.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/deef804fae0c4fa08e7b1b901925419a#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\deef804fae0c4fa08e7b1b901925419a.webp", "FileLength": 97050, "LastWriteTime": "2025-07-18T23:45:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e7ddc387739a443ab47f3c0d18e7cab2.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/e7ddc387739a443ab47f3c0d18e7cab2#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\e7ddc387739a443ab47f3c0d18e7cab2.png", "FileLength": 235486, "LastWriteTime": "2025-07-28T18:36:40+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/e97afe0fea7d46fa90ae97941b717777#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "FileLength": 195627, "LastWriteTime": "2025-06-23T14:51:09+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bm200vq19x", "Integrity": "Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "FileLength": 51973, "LastWriteTime": "2025-06-23T15:25:34+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj02sd2q5h", "Integrity": "wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "FileLength": 1280957, "LastWriteTime": "2025-06-24T14:09:22+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f5fa12ce51fd47b39a7341711e685eeb.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f5fa12ce51fd47b39a7341711e685eeb#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "42qs53m74g", "Integrity": "7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f5fa12ce51fd47b39a7341711e685eeb.png", "FileLength": 180218, "LastWriteTime": "2025-07-12T00:46:34+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f706c5792702492a90e00628f2eb9ba6#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "FileLength": 97050, "LastWriteTime": "2025-06-23T02:38:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f8a5a58c11b945118610694f03b0007e.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f8a5a58c11b945118610694f03b0007e#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f8a5a58c11b945118610694f03b0007e.png", "FileLength": 195627, "LastWriteTime": "2025-07-18T23:44:54+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f93ad881856442cd850032c437ad256a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f93ad881856442cd850032c437ad256a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f93ad881856442cd850032c437ad256a.png", "FileLength": 196383, "LastWriteTime": "2025-07-13T03:31:16+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/f97829c0fe164622a2e1eae38e40117a#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "t3dbuv7mfy", "Integrity": "GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "FileLength": 216207, "LastWriteTime": "2025-06-23T15:48:04+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/fb22d922074c4743951c4e34232de868#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "eb9ax9ytkh", "Integrity": "Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "FileLength": 726022, "LastWriteTime": "2025-06-23T17:58:32+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pnn48fdjlm", "Integrity": "xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "FileLength": 196383, "LastWriteTime": "2025-06-23T15:50:37+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\feda33a57a2243d2af4b1db8688f2f88.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/feda33a57a2243d2af4b1db8688f2f88#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\feda33a57a2243d2af4b1db8688f2f88.webp", "FileLength": 97050, "LastWriteTime": "2025-07-17T15:32:15+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ff4a6481da70456989622e13b2250a71.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/images/ff4a6481da70456989622e13b2250a71#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\images\\ff4a6481da70456989622e13b2250a71.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-13T03:26:48+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/11073ad0-55de-42bb-bc0e-e0a215069961_10#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qm5cpjs4gn", "Integrity": "gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "FileLength": 32278, "LastWriteTime": "2025-07-14T19:49:19+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/4b03c2c9-c7c4-482c-91d9-964d60405f38_123#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vcjj9guxre", "Integrity": "v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "FileLength": 46057, "LastWriteTime": "2025-07-14T19:43:57+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vfb68j65u8", "Integrity": "a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "FileLength": 195627, "LastWriteTime": "2025-07-14T19:58:39+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-14T20:24:05+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/6da59366-0bed-4102-9711-e5f17991bfb2_controller#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-14T19:51:33+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/b8061978-90a1-4e3d-b474-328770509051_controller#[.{fingerprint}]?.PNG", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c69f109cb6", "Integrity": "zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "FileLength": 1011297, "LastWriteTime": "2025-07-14T20:09:27+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/message-attachments/b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5za9gpxx61", "Integrity": "L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\message-attachments\\b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "FileLength": 97050, "LastWriteTime": "2025-07-14T20:27:49+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\16_20250716_025728.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/16_20250716_025728#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1756gt4fxa", "Integrity": "M/xRRTChhHnenO7cjk9l7Fz/Swl3F9ZwmyRVYD+06zo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\16_20250716_025728.png", "FileLength": 6260, "LastWriteTime": "2025-07-16T02:57:28+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\17_20250716_020611.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/17_20250716_020611#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "umutyta48r", "Integrity": "VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\17_20250716_020611.png", "FileLength": 235486, "LastWriteTime": "2025-07-16T02:06:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\18_20250716_025530.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/18_20250716_025530#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9zr8ou7lzf", "Integrity": "pT8w2MTjFI9Wh4oTtxB0BacHV7dU9BBHBz/i+Rt5J/w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\18_20250716_025530.png", "FileLength": 11549, "LastWriteTime": "2025-07-16T02:55:30+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\19_20250716_025433.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/19_20250716_025433#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zhkndqnwdu", "Integrity": "efP1WwguX56r5uUDkiIDBY5KbRnrjWms31lNUZkMpJE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\19_20250716_025433.png", "FileLength": 11348, "LastWriteTime": "2025-07-16T02:54:33+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\20_20250716_011214.webp", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/20_20250716_011214#[.{fingerprint}]?.webp", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zl0dp0irm3", "Integrity": "qSIka5nUZPuD5eWm7tuKkIX7GWOxp7PsLr0ePtrICLQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\20_20250716_011214.webp", "FileLength": 30508, "LastWriteTime": "2025-07-16T01:12:14+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232513.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_232513#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fago7df5v7", "Integrity": "iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_232513.png", "FileLength": 25896, "LastWriteTime": "2025-07-15T23:25:13+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232624.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_232624#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fago7df5v7", "Integrity": "iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_232624.png", "FileLength": 25896, "LastWriteTime": "2025-07-15T23:26:24+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_233121.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_233121#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "3nzxj0mxb8", "Integrity": "r8U7ycyf7PZ9VviNH+TH0rSP7i10UNOhHGipCOVfj0Y=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_233121.png", "FileLength": 20215, "LastWriteTime": "2025-07-15T23:31:21+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234020.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_234020#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9xcst1d5jn", "Integrity": "bSGwTRkG7JJxSWJtpxKk9RKm33lzvZ2gGnNM25GT220=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_234020.png", "FileLength": 29764, "LastWriteTime": "2025-07-15T23:40:20+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234816.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_234816#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zxkacwu4rx", "Integrity": "h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_234816.png", "FileLength": 9693, "LastWriteTime": "2025-07-15T23:48:16+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_235443.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250715_235443#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fago7df5v7", "Integrity": "iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250715_235443.png", "FileLength": 25896, "LastWriteTime": "2025-07-15T23:54:43+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250716_011418.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250716_011418#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ioah74vzm7", "Integrity": "UvcxzcwgzCx8g+I327YOPvr20fUIBUNpuVz4nhBLYfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250716_011418.png", "FileLength": 54472, "LastWriteTime": "2025-07-16T01:14:18+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250723_161244.jpg", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/21_20250723_161244#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4cnl9qiz9v", "Integrity": "ZJrx2dySBKYK4ZIGHGmiAevZtOSzIhl/q3Nh6hF60kE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\21_20250723_161244.jpg", "FileLength": 20451, "LastWriteTime": "2025-07-23T16:12:44+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\22_20250716_021402.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/22_20250716_021402#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "682niy8knh", "Integrity": "+vqXtzH1M17PVpGf9urXk3WkxZwARmvwIl0eXTwif68=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\22_20250716_021402.png", "FileLength": 9849, "LastWriteTime": "2025-07-16T02:14:02+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\23_20250716_021342.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/23_20250716_021342#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vv893ww0lz", "Integrity": "O/hKeg4sEpZq5/3WqwlklbVMeWck9RGLYISA5BFbFp0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\23_20250716_021342.png", "FileLength": 11910, "LastWriteTime": "2025-07-16T02:13:42+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_011509.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/24_20250716_011509#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8zdxq49pmb", "Integrity": "/IAlo7QO6ZGzDM12/CZtlMX9fUPMCx7rzRTkjAcnuyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\24_20250716_011509.png", "FileLength": 13303, "LastWriteTime": "2025-07-16T01:15:09+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_021311.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/24_20250716_021311#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "l3bd4ywp3d", "Integrity": "cQVnTrpbjdZc9IektUY9shkviCjS3BCAj9YtwMGsXyI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\24_20250716_021311.png", "FileLength": 18938, "LastWriteTime": "2025-07-16T02:13:11+00:00"}, {"Identity": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\29_20250716_011138.png", "SourceId": "webApi", "SourceType": "Discovered", "ContentRoot": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\", "BasePath": "_content/webApi", "RelativePath": "uploads/profile_images/29_20250716_011138#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zxkacwu4rx", "Integrity": "h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\uploads\\profile_images\\29_20250716_011138.png", "FileLength": 9693, "LastWriteTime": "2025-07-16T01:11:38+00:00"}], "Endpoints": [{"Route": "backups/backup_20250707_153306_0.3nitcostfa.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250707_153306_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YRBe6ysvjXyhkPDFQzZZqYRiwyPmqlD5uakf7sPyy/8=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 15:33:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3nitcostfa"}, {"Name": "label", "Value": "backups/backup_20250707_153306_0.sql"}, {"Name": "integrity", "Value": "sha256-YRBe6ysvjXyhkPDFQzZZqYRiwyPmqlD5uakf7sPyy/8="}]}, {"Route": "backups/backup_20250707_153306_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250707_153306_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YRBe6ysvjXyhkPDFQzZZqYRiwyPmqlD5uakf7sPyy/8=\""}, {"Name": "Last-Modified", "Value": "Mon, 07 Jul 2025 15:33:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YRBe6ysvjXyhkPDFQzZZqYRiwyPmqlD5uakf7sPyy/8="}]}, {"Route": "backups/backup_20250712_033808_0.18we1jq8q4.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250712_033808_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35742208"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"oQeieJeQARhNT+LAEDSPxfV6ph1EDZW9Mc0jps4OIjk=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 03:38:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "18we1jq8q4"}, {"Name": "label", "Value": "backups/backup_20250712_033808_0.sql"}, {"Name": "integrity", "Value": "sha256-oQeieJeQARhNT+LAEDSPxfV6ph1EDZW9Mc0jps4OIjk="}]}, {"Route": "backups/backup_20250712_033808_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250712_033808_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "35742208"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"oQeieJeQARhNT+LAEDSPxfV6ph1EDZW9Mc0jps4OIjk=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 03:38:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-oQeieJeQARhNT+LAEDSPxfV6ph1EDZW9Mc0jps4OIjk="}]}, {"Route": "backups/backup_20250715_141356_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_141356_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64536064"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iFx6term7Iii94BvNRILue9lfdAxsIF4l2QmqWMRkx4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:44:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iFx6term7Iii94BvNRILue9lfdAxsIF4l2QmqWMRkx4="}]}, {"Route": "backups/backup_20250715_141356_0.wei1ctf611.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_141356_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "64536064"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"iFx6term7Iii94BvNRILue9lfdAxsIF4l2QmqWMRkx4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:44:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wei1ctf611"}, {"Name": "label", "Value": "backups/backup_20250715_141356_0.sql"}, {"Name": "integrity", "Value": "sha256-iFx6term7Iii94BvNRILue9lfdAxsIF4l2QmqWMRkx4="}]}, {"Route": "backups/backup_20250715_144541_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144541_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"otlzc+n4ZtXnuOb3H8oOLLeUzfrsLht7Fbj6JYZa7Vo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:45:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-otlzc+n4ZtXnuOb3H8oOLLeUzfrsLht7Fbj6JYZa7Vo="}]}, {"Route": "backups/backup_20250715_144541_0.u0kw9kgy4w.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144541_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"otlzc+n4ZtXnuOb3H8oOLLeUzfrsLht7Fbj6JYZa7Vo=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:45:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u0kw9kgy4w"}, {"Name": "label", "Value": "backups/backup_20250715_144541_0.sql"}, {"Name": "integrity", "Value": "sha256-otlzc+n4ZtXnuOb3H8oOLLeUzfrsLht7Fbj6JYZa7Vo="}]}, {"Route": "backups/backup_20250715_144735_0.olba8xqros.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144735_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"POXDw4nr2OA8/3aowe2zlxCrBs7mXmm+YfksbHdGlbk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:47:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "olba8xqros"}, {"Name": "label", "Value": "backups/backup_20250715_144735_0.sql"}, {"Name": "integrity", "Value": "sha256-POXDw4nr2OA8/3aowe2zlxCrBs7mXmm+YfksbHdGlbk="}]}, {"Route": "backups/backup_20250715_144735_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_144735_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"POXDw4nr2OA8/3aowe2zlxCrBs7mXmm+YfksbHdGlbk=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 14:47:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-POXDw4nr2OA8/3aowe2zlxCrBs7mXmm+YfksbHdGlbk="}]}, {"Route": "backups/backup_20250715_150009_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150009_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"a152NpsfczkDHXjEOwKc4ScwQ/u8Waryi4+07uGzY3U=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:00:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a152NpsfczkDHXjEOwKc4ScwQ/u8Waryi4+07uGzY3U="}]}, {"Route": "backups/backup_20250715_150009_0.zy0q7wnq3f.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150009_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"a152NpsfczkDHXjEOwKc4ScwQ/u8Waryi4+07uGzY3U=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:00:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zy0q7wnq3f"}, {"Name": "label", "Value": "backups/backup_20250715_150009_0.sql"}, {"Name": "integrity", "Value": "sha256-a152NpsfczkDHXjEOwKc4ScwQ/u8Waryi4+07uGzY3U="}]}, {"Route": "backups/backup_20250715_150334_0.jwjdiclzr9.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150334_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"A7iUtACQkR9+3W8ck0fZPDLcJejcMLHZ7NlDvxMuJx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:03:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jwjdiclzr9"}, {"Name": "label", "Value": "backups/backup_20250715_150334_0.sql"}, {"Name": "integrity", "Value": "sha256-A7iUtACQkR9+3W8ck0fZPDLcJejcMLHZ7NlDvxMuJx0="}]}, {"Route": "backups/backup_20250715_150334_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_150334_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"A7iUtACQkR9+3W8ck0fZPDLcJejcMLHZ7NlDvxMuJx0=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:03:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-A7iUtACQkR9+3W8ck0fZPDLcJejcMLHZ7NlDvxMuJx0="}]}, {"Route": "backups/backup_20250715_151225_0.d77ue2n4io.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151225_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"B/qYvn8Pf2u6auT+G2yh7pkwhujz/k7yZAEYwGTKpfY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:12:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d77ue2n4io"}, {"Name": "label", "Value": "backups/backup_20250715_151225_0.sql"}, {"Name": "integrity", "Value": "sha256-B/qYvn8Pf2u6auT+G2yh7pkwhujz/k7yZAEYwGTKpfY="}]}, {"Route": "backups/backup_20250715_151225_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151225_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"B/qYvn8Pf2u6auT+G2yh7pkwhujz/k7yZAEYwGTKpfY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:12:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-B/qYvn8Pf2u6auT+G2yh7pkwhujz/k7yZAEYwGTKpfY="}]}, {"Route": "backups/backup_20250715_151229_0.mq6ep6mvf6.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151229_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YjU5e2A+IpX/NzDdAy8M8RWuFn+/HBToRTFV8KAfuJs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:12:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mq6ep6mvf6"}, {"Name": "label", "Value": "backups/backup_20250715_151229_0.sql"}, {"Name": "integrity", "Value": "sha256-YjU5e2A+IpX/NzDdAy8M8RWuFn+/HBToRTFV8KAfuJs="}]}, {"Route": "backups/backup_20250715_151229_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_151229_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"YjU5e2A+IpX/NzDdAy8M8RWuFn+/HBToRTFV8KAfuJs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:12:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YjU5e2A+IpX/NzDdAy8M8RWuFn+/HBToRTFV8KAfuJs="}]}, {"Route": "backups/backup_20250715_153548_0.hxng2sfg3k.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153548_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Jf+h2r8SstAga8I8YNaDLIDabcFU/4P559RR/vHiViY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:35:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hxng2sfg3k"}, {"Name": "label", "Value": "backups/backup_20250715_153548_0.sql"}, {"Name": "integrity", "Value": "sha256-Jf+h2r8SstAga8I8YNaDLIDabcFU/4P559RR/vHiViY="}]}, {"Route": "backups/backup_20250715_153548_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153548_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Jf+h2r8SstAga8I8YNaDLIDabcFU/4P559RR/vHiViY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:35:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jf+h2r8SstAga8I8YNaDLIDabcFU/4P559RR/vHiViY="}]}, {"Route": "backups/backup_20250715_153556_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153556_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2GseJ5dWr/4A3VO2DLXr8mtyw9grW7EaLReEjDNaJMA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:35:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2GseJ5dWr/4A3VO2DLXr8mtyw9grW7EaLReEjDNaJMA="}]}, {"Route": "backups/backup_20250715_153556_0.wyz7qo4ryg.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153556_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32072192"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"2GseJ5dWr/4A3VO2DLXr8mtyw9grW7EaLReEjDNaJMA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 15:35:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wyz7qo4ryg"}, {"Name": "label", "Value": "backups/backup_20250715_153556_0.sql"}, {"Name": "integrity", "Value": "sha256-2GseJ5dWr/4A3VO2DLXr8mtyw9grW7EaLReEjDNaJMA="}]}, {"Route": "backups/backup_20250715_153621_0.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153621_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107354624"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PmiGQrx/QjOK9CoJe94vh7pezoZD3G+AZI6tCh4lLqk=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 19:50:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PmiGQrx/QjOK9CoJe94vh7pezoZD3G+AZI6tCh4lLqk="}]}, {"Route": "backups/backup_20250715_153621_0.yosmphs06q.sql", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\backups\\backup_20250715_153621_0.sql", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107354624"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"PmiGQrx/QjOK9CoJe94vh7pezoZD3G+AZI6tCh4lLqk=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 19:50:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yosmphs06q"}, {"Name": "label", "Value": "backups/backup_20250715_153621_0.sql"}, {"Name": "integrity", "Value": "sha256-PmiGQrx/QjOK9CoJe94vh7pezoZD3G+AZI6tCh4lLqk="}]}, {"Route": "uploads/archives/be70474c4b4744bc9edf86d5b08c891e.6j92lcaaoh.rar", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\archives\\be70474c4b4744bc9edf86d5b08c891e.rar", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16312166"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Pl7DPbWsz7LGbqR3M1VbuZkvPowr6LTDhbz24fXxpag=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 17:42:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6j92<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "uploads/archives/be70474c4b4744bc9edf86d5b08c891e.rar"}, {"Name": "integrity", "Value": "sha256-Pl7DPbWsz7LGbqR3M1VbuZkvPowr6LTDhbz24fXxpag="}]}, {"Route": "uploads/archives/be70474c4b4744bc9edf86d5b08c891e.rar", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\archives\\be70474c4b4744bc9edf86d5b08c891e.rar", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16312166"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"Pl7DPbWsz7LGbqR3M1VbuZkvPowr6LTDhbz24fXxpag=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 17:42:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Pl7DPbWsz7LGbqR3M1VbuZkvPowr6LTDhbz24fXxpag="}]}, {"Route": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 22 Jun 2025 15:42:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/attachments/4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\4b6c9174-7b58-40fb-8ffb-0b6673929573_تقارير صوره نموذدج.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 22 Jun 2025 15:42:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pbhq5urrew.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:14:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbhq5urrew"}, {"Name": "label", "Value": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf"}, {"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/attachments/72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\72450ef0-67b2-45be-b7c4-0775713fe5e8_ww.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:14:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091955"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 21:20:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE="}]}, {"Route": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.q5n9oh9rw1.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091955"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 21:20:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q5n9oh9rw1"}, {"Name": "label", "Value": "uploads/attachments/92016494-8eeb-4f26-bc61-0307b2cfdeca_eee.pdf"}, {"Name": "integrity", "Value": "sha256-gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).dz1rjozczi.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000132450331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dz1rjozczi"}, {"Name": "label", "Value": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt"}, {"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).dz1rjozczi.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28469"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:16:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dz1rjozczi"}, {"Name": "label", "Value": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt"}, {"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).dz1rjozczi.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dz1rjozczi"}, {"Name": "label", "Value": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt.gz"}, {"Name": "integrity", "Value": "sha256-II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000132450331"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\attachments\\b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "28469"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE=\""}, {"Name": "Last-Modified", "Value": "Fri, 20 Jun 2025 19:16:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NzpxOt9Y0vrfe1Ss2JIpNPRWsukHFzNVN/8OT8y2hXE="}]}, {"Route": "uploads/attachments/b95dd3f8-2612-4857-9f7a-052bd5b4d961_مستند نصي جديد (2).txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\xoelc6sdnq-dz1rjozczi.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7549"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-II89AIAfepVyxMAEBSxXRGF+cpbNgb15nTDmDlReLDI="}]}, {"Route": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000190585096"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5246"}, {"Name": "ETag", "Value": "\"at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "W/\"+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ="}]}, {"Route": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24217"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 21:56:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ="}]}, {"Route": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.csv.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5246"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo="}]}, {"Route": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.je3vbe9eb6.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000190585096"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5246"}, {"Name": "ETag", "Value": "\"at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "W/\"+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "je3vbe9eb6"}, {"Name": "label", "Value": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.csv"}, {"Name": "integrity", "Value": "sha256-+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ="}]}, {"Route": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.je3vbe9eb6.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\3f3113bbbb4d489b83401dff8c0fdfdd.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "24217"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 21:56:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "je3vbe9eb6"}, {"Name": "label", "Value": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.csv"}, {"Name": "integrity", "Value": "sha256-+xWHNCccBidayBesuXIPA8YZNclsPpeJNlqvJ8q7ghQ="}]}, {"Route": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.je3vbe9eb6.csv.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ad4wzmw55x-je3vbe9eb6.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5246"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "je3vbe9eb6"}, {"Name": "label", "Value": "uploads/data/3f3113bbbb4d489b83401dff8c0fdfdd.csv.gz"}, {"Name": "integrity", "Value": "sha256-at5YydYhql7IaKEFsOXJvgNUvkegiGcimAq420+jGOo="}]}, {"Route": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001321003963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "W/\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3535"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 19:08:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.csv.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo="}]}, {"Route": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.ecem5x2v5n.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001321003963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "W/\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ecem5x2v5n"}, {"Name": "label", "Value": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.csv"}, {"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.ecem5x2v5n.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\8f6873cdd5814931959cd8b81db2d7fa.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3535"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 19:08:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ecem5x2v5n"}, {"Name": "label", "Value": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.csv"}, {"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.ecem5x2v5n.csv.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\f2eqwau0gr-ecem5x2v5n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ecem5x2v5n"}, {"Name": "label", "Value": "uploads/data/8f6873cdd5814931959cd8b81db2d7fa.csv.gz"}, {"Name": "integrity", "Value": "sha256-C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo="}]}, {"Route": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001321003963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "W/\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3535"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 19:11:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.csv.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo="}]}, {"Route": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.ecem5x2v5n.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.001321003963"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "W/\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ecem5x2v5n"}, {"Name": "label", "Value": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.csv"}, {"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.ecem5x2v5n.csv", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\data\\a85476dcc9b944f48a6c942f9ffe5e76.csv", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3535"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8=\""}, {"Name": "Last-Modified", "Value": "Sat, 19 Jul 2025 19:11:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ecem5x2v5n"}, {"Name": "label", "Value": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.csv"}, {"Name": "integrity", "Value": "sha256-4uSXnqUumT7WfwvzKRkPZUEXRI3wetfz0saHsCd47b8="}]}, {"Route": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.ecem5x2v5n.csv.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\nr15fqvel8-ecem5x2v5n.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "756"}, {"Name": "Content-Type", "Value": "text/csv"}, {"Name": "ETag", "Value": "\"C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ecem5x2v5n"}, {"Name": "label", "Value": "uploads/data/a85476dcc9b944f48a6c942f9ffe5e76.csv.gz"}, {"Name": "integrity", "Value": "sha256-C5qk/rCvKBvaTjxthuNA+IlQXOXtvrUA6YsdWwLidXo="}]}, {"Route": "uploads/documents/02cc06431c17416889e85b70a05142e2.cgulgik6pq.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\02cc06431c17416889e85b70a05142e2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:20:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cgulgik6pq"}, {"Name": "label", "Value": "uploads/documents/02cc06431c17416889e85b70a05142e2.pdf"}, {"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/02cc06431c17416889e85b70a05142e2.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\02cc06431c17416889e85b70a05142e2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:20:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/052eb05336464289832f7814ce2d7630.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\052eb05336464289832f7814ce2d7630.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:16:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/052eb05336464289832f7814ce2d7630.v8f3tesalf.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\052eb05336464289832f7814ce2d7630.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:16:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v8f3tesalf"}, {"Name": "label", "Value": "uploads/documents/052eb05336464289832f7814ce2d7630.docx"}, {"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/0717d4b8b05f4122a2163034c59e5f22.gonabiiekc.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0717d4b8b05f4122a2163034c59e5f22.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91721"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Jul 2025 23:41:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gonabiiekc"}, {"Name": "label", "Value": "uploads/documents/0717d4b8b05f4122a2163034c59e5f22.pdf"}, {"Name": "integrity", "Value": "sha256-YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs="}]}, {"Route": "uploads/documents/0717d4b8b05f4122a2163034c59e5f22.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0717d4b8b05f4122a2163034c59e5f22.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91721"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Jul 2025 23:41:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs="}]}, {"Route": "uploads/documents/0dfd9a3304db455a859caab855e678f4.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0dfd9a3304db455a859caab855e678f4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091955"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 01:56:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE="}]}, {"Route": "uploads/documents/0dfd9a3304db455a859caab855e678f4.q5n9oh9rw1.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\0dfd9a3304db455a859caab855e678f4.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1091955"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 01:56:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q5n9oh9rw1"}, {"Name": "label", "Value": "uploads/documents/0dfd9a3304db455a859caab855e678f4.pdf"}, {"Name": "integrity", "Value": "sha256-gtawLKU9MeLXPsHO2bX9QSQhTx2tl05g/IqIzJXrKJE="}]}, {"Route": "uploads/documents/18e70463ba004856925a8d0e72b2b242.e1igdv5m66.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\18e70463ba004856925a8d0e72b2b242.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 02:16:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1igdv5m66"}, {"Name": "label", "Value": "uploads/documents/18e70463ba004856925a8d0e72b2b242.pdf"}, {"Name": "integrity", "Value": "sha256-UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI="}]}, {"Route": "uploads/documents/18e70463ba004856925a8d0e72b2b242.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\18e70463ba004856925a8d0e72b2b242.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 02:16:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI="}]}, {"Route": "uploads/documents/1d66653fc02145d3a7be0572160c3eb0.kpn9enu225.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\1d66653fc02145d3a7be0572160c3eb0.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303771"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"SMpop/jX5zRJnJyEOe34+XpqryRJZKVLM6GLh7a0EJM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:41:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kpn9enu225"}, {"Name": "label", "Value": "uploads/documents/1d66653fc02145d3a7be0572160c3eb0.pdf"}, {"Name": "integrity", "Value": "sha256-SMpop/jX5zRJnJyEOe34+XpqryRJZKVLM6GLh7a0EJM="}]}, {"Route": "uploads/documents/1d66653fc02145d3a7be0572160c3eb0.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\1d66653fc02145d3a7be0572160c3eb0.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303771"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"SMpop/jX5zRJnJyEOe34+XpqryRJZKVLM6GLh7a0EJM=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:41:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SMpop/jX5zRJnJyEOe34+XpqryRJZKVLM6GLh7a0EJM="}]}, {"Route": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0.pbhq5urrew.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:41:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbhq5urrew"}, {"Name": "label", "Value": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0.pdf"}, {"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/22efc5c1e7a043d68729a9b15788b8d0.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\22efc5c1e7a043d68729a9b15788b8d0.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:41:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:41:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.knc7nr4hqg.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt.gz"}, {"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\341c09fc1cb5436caa484b2c0e1dd5ca.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:41:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/341c09fc1cb5436caa484b2c0e1dd5ca.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\ph3rqhnt6w-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/3765e865484c4ea39fc07eb5a9182354.cgulgik6pq.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3765e865484c4ea39fc07eb5a9182354.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:24:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cgulgik6pq"}, {"Name": "label", "Value": "uploads/documents/3765e865484c4ea39fc07eb5a9182354.pdf"}, {"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/3765e865484c4ea39fc07eb5a9182354.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3765e865484c4ea39fc07eb5a9182354.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:24:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/37b012f3d1584ff38e3dd454fe705980.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\37b012f3d1584ff38e3dd454fe705980.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 15:05:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/37b012f3d1584ff38e3dd454fe705980.v8f3tesalf.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\37b012f3d1584ff38e3dd454fe705980.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Tue, 08 Jul 2025 15:05:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v8f3tesalf"}, {"Name": "label", "Value": "uploads/documents/37b012f3d1584ff38e3dd454fe705980.docx"}, {"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/3982df413cf14caf9da852e86398c3f2.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 17:35:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/3982df413cf14caf9da852e86398c3f2.yqs7wwjalp.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\3982df413cf14caf9da852e86398c3f2.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 17:35:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqs7wwjalp"}, {"Name": "label", "Value": "uploads/documents/3982df413cf14caf9da852e86398c3f2.pdf"}, {"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/429aa494b4f6454aa6a35873f7c8401f.mqlqasmzgb.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\429aa494b4f6454aa6a35873f7c8401f.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "441546"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"crWI8ObgLXTKW55S4HeY98kcaiPSPgifDt/PhRrL8lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 16:01:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mqlqasmzgb"}, {"Name": "label", "Value": "uploads/documents/429aa494b4f6454aa6a35873f7c8401f.pdf"}, {"Name": "integrity", "Value": "sha256-crWI8ObgLXTKW55S4HeY98kcaiPSPgifDt/PhRrL8lg="}]}, {"Route": "uploads/documents/429aa494b4f6454aa6a35873f7c8401f.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\429aa494b4f6454aa6a35873f7c8401f.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "441546"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"crWI8ObgLXTKW55S4HeY98kcaiPSPgifDt/PhRrL8lg=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 16:01:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-crWI8ObgLXTKW55S4HeY98kcaiPSPgifDt/PhRrL8lg="}]}, {"Route": "uploads/documents/458bc2d8a56c42e4aca145ca1cdbf135.et9dbzk0w9.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\458bc2d8a56c42e4aca145ca1cdbf135.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25855"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:26:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "et9dbzk0w9"}, {"Name": "label", "Value": "uploads/documents/458bc2d8a56c42e4aca145ca1cdbf135.pdf"}, {"Name": "integrity", "Value": "sha256-DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk="}]}, {"Route": "uploads/documents/458bc2d8a56c42e4aca145ca1cdbf135.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\458bc2d8a56c42e4aca145ca1cdbf135.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25855"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:26:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk="}]}, {"Route": "uploads/documents/48082c73ee21481e8b2b10f9300d66bc.3cceavjbna.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\48082c73ee21481e8b2b10f9300d66bc.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1226126"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"80su5ua6RMNKr09mBcSYxyWu6S68zXgD4tniX2F372k=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:07:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3cceavjbna"}, {"Name": "label", "Value": "uploads/documents/48082c73ee21481e8b2b10f9300d66bc.pdf"}, {"Name": "integrity", "Value": "sha256-80su5ua6RMNKr09mBcSYxyWu6S68zXgD4tniX2F372k="}]}, {"Route": "uploads/documents/48082c73ee21481e8b2b10f9300d66bc.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\48082c73ee21481e8b2b10f9300d66bc.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1226126"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"80su5ua6RMNKr09mBcSYxyWu6S68zXgD4tniX2F372k=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:07:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-80su5ua6RMNKr09mBcSYxyWu6S68zXgD4tniX2F372k="}]}, {"Route": "uploads/documents/57cc983c754e400fa8630e31eae1bc49.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:09:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/57cc983c754e400fa8630e31eae1bc49.yqs7wwjalp.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\57cc983c754e400fa8630e31eae1bc49.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:09:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqs7wwjalp"}, {"Name": "label", "Value": "uploads/documents/57cc983c754e400fa8630e31eae1bc49.pdf"}, {"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/597d0e98415a4c19b8d7267d6e4c096a.cgulgik6pq.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\597d0e98415a4c19b8d7267d6e4c096a.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:27:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cgulgik6pq"}, {"Name": "label", "Value": "uploads/documents/597d0e98415a4c19b8d7267d6e4c096a.pdf"}, {"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/597d0e98415a4c19b8d7267d6e4c096a.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\597d0e98415a4c19b8d7267d6e4c096a.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:27:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/5b9afba8483c42c688d10268e402bf63.gonabiiekc.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\5b9afba8483c42c688d10268e402bf63.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91721"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:05:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gonabiiekc"}, {"Name": "label", "Value": "uploads/documents/5b9afba8483c42c688d10268e402bf63.pdf"}, {"Name": "integrity", "Value": "sha256-YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs="}]}, {"Route": "uploads/documents/5b9afba8483c42c688d10268e402bf63.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\5b9afba8483c42c688d10268e402bf63.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91721"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:05:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs="}]}, {"Route": "uploads/documents/6d49532932024faf81b6cc20c91ba142.cgulgik6pq.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\6d49532932024faf81b6cc20c91ba142.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:24:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cgulgik6pq"}, {"Name": "label", "Value": "uploads/documents/6d49532932024faf81b6cc20c91ba142.pdf"}, {"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/6d49532932024faf81b6cc20c91ba142.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\6d49532932024faf81b6cc20c91ba142.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:24:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/7091b5e0eb5f41929191868a1ccdb942.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\7091b5e0eb5f41929191868a1ccdb942.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:45:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/7091b5e0eb5f41929191868a1ccdb942.yqs7wwjalp.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\7091b5e0eb5f41929191868a1ccdb942.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:45:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqs7wwjalp"}, {"Name": "label", "Value": "uploads/documents/7091b5e0eb5f41929191868a1ccdb942.pdf"}, {"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90.6mabyag1xn.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:05:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6mabyag1xn"}, {"Name": "label", "Value": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90.pdf"}, {"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/776d38afde4e4189ad9a86c615b5ab90.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\776d38afde4e4189ad9a86c615b5ab90.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:05:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/8375e14b17a341efb982c00a4344db2c.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:46:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/8375e14b17a341efb982c00a4344db2c.v8f3tesalf.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8375e14b17a341efb982c00a4344db2c.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:46:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v8f3tesalf"}, {"Name": "label", "Value": "uploads/documents/8375e14b17a341efb982c00a4344db2c.docx"}, {"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/838f2c3b1144437183c209880c5ddf08.lnw1de61i1.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\838f2c3b1144437183c209880c5ddf08.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7245"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"kSBidZdGBlsfJZ3Kas8ksc+ppgV93hDK66H8YS77dus=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:41:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lnw1de61i1"}, {"Name": "label", "Value": "uploads/documents/838f2c3b1144437183c209880c5ddf08.pdf"}, {"Name": "integrity", "Value": "sha256-kSBidZdGBlsfJZ3Kas8ksc+ppgV93hDK66H8YS77dus="}]}, {"Route": "uploads/documents/838f2c3b1144437183c209880c5ddf08.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\838f2c3b1144437183c209880c5ddf08.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7245"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"kSBidZdGBlsfJZ3Kas8ksc+ppgV93hDK66H8YS77dus=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:41:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kSBidZdGBlsfJZ3Kas8ksc+ppgV93hDK66H8YS77dus="}]}, {"Route": "uploads/documents/84b9165f75994121b64d6ab3a05d38fd.cgulgik6pq.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\84b9165f75994121b64d6ab3a05d38fd.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 03:14:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cgulgik6pq"}, {"Name": "label", "Value": "uploads/documents/84b9165f75994121b64d6ab3a05d38fd.pdf"}, {"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/84b9165f75994121b64d6ab3a05d38fd.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\84b9165f75994121b64d6ab3a05d38fd.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 03:14:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.knc7nr4hqg.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:18:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt"}, {"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.knc7nr4hqg.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "knc7nr4hqg"}, {"Name": "label", "Value": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt.gz"}, {"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [{"Name": "Content-Encoding", "Value": "gzip", "Quality": "0.000518672199"}], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "W/\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\8df256e6ebd646bea1759f1a8c0c042e.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7630"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:18:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MIHVjvVeSqqQQ32imft8LHKwTjLcuCBBmCXqEbSwkVY="}]}, {"Route": "uploads/documents/8df256e6ebd646bea1759f1a8c0c042e.txt.gz", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\obj\\Debug\\net9.0\\compressed\\v25brddiup-knc7nr4hqg.gz", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1927"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:42:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}, {"Name": "Content-Encoding", "Value": "gzip"}, {"Name": "Vary", "Value": "Content-Encoding"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UW3ARA4/Sz7LAQP6gY5P45wxbgbca+CKw99aaHBkti4="}]}, {"Route": "uploads/documents/a24bf33b26f34799b3d0330291adf0fc.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a24bf33b26f34799b3d0330291adf0fc.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 02:38:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/a24bf33b26f34799b3d0330291adf0fc.yqs7wwjalp.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a24bf33b26f34799b3d0330291adf0fc.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "515576"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 02:38:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yqs7wwjalp"}, {"Name": "label", "Value": "uploads/documents/a24bf33b26f34799b3d0330291adf0fc.pdf"}, {"Name": "integrity", "Value": "sha256-St2DTbCwbHMhcDHVtYFZ/qVRPCUxwg5bzClji06W2Lo="}]}, {"Route": "uploads/documents/a91fd552aae140a186b9f82e91f6d071.cgulgik6pq.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a91fd552aae140a186b9f82e91f6d071.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:22:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cgulgik6pq"}, {"Name": "label", "Value": "uploads/documents/a91fd552aae140a186b9f82e91f6d071.pdf"}, {"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/a91fd552aae140a186b9f82e91f6d071.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\a91fd552aae140a186b9f82e91f6d071.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1303767"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:22:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lBoT5y5R32KJsWPLoid04XDbZWVi3uRWYSr+cQdhjUQ="}]}, {"Route": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02.pbhq5urrew.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pbhq5urrew"}, {"Name": "label", "Value": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02.pdf"}, {"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/b90557820bb340bd96d5fc46c4f97c02.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\b90557820bb340bd96d5fc46c4f97c02.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1607"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nad3FRrqic58l/7bXC6SmDDqrxeDpTkJoX2Kp8/MJ+4="}]}, {"Route": "uploads/documents/bff0abcb17524585a06eee3255b1f49a.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:48:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/bff0abcb17524585a06eee3255b1f49a.v8f3tesalf.docx", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\bff0abcb17524585a06eee3255b1f49a.docx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2149873"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.wordprocessingml.document"}, {"Name": "ETag", "Value": "\"7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:48:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "v8f3tesalf"}, {"Name": "label", "Value": "uploads/documents/bff0abcb17524585a06eee3255b1f49a.docx"}, {"Name": "integrity", "Value": "sha256-7+TT+/03/schKRHLdX5neR9O+uaviQgYWeWlqYcdMA8="}]}, {"Route": "uploads/documents/c3d68bb6815944c38541f95cdc697682.nmzbkjlql8.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\c3d68bb6815944c38541f95cdc697682.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23575"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 03:34:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nmzbkjlql8"}, {"Name": "label", "Value": "uploads/documents/c3d68bb6815944c38541f95cdc697682.pdf"}, {"Name": "integrity", "Value": "sha256-4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY="}]}, {"Route": "uploads/documents/c3d68bb6815944c38541f95cdc697682.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\c3d68bb6815944c38541f95cdc697682.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23575"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 03:34:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY="}]}, {"Route": "uploads/documents/cb01a1c7ff704836b8a4575cebba0f7d.nmzbkjlql8.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\cb01a1c7ff704836b8a4575cebba0f7d.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23575"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 17:26:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nmzbkjlql8"}, {"Name": "label", "Value": "uploads/documents/cb01a1c7ff704836b8a4575cebba0f7d.pdf"}, {"Name": "integrity", "Value": "sha256-4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY="}]}, {"Route": "uploads/documents/cb01a1c7ff704836b8a4575cebba0f7d.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\cb01a1c7ff704836b8a4575cebba0f7d.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "23575"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 17:26:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4ebosyEubKGQS6qV59F+wqf8UmEIw2RAUtRSVfEoemY="}]}, {"Route": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1.o5mrvqq7ms.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "147799"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:59:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o5mrvqq7ms"}, {"Name": "label", "Value": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1.pdf"}, {"Name": "integrity", "Value": "sha256-7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc="}]}, {"Route": "uploads/documents/d6b0e189847a4d1293e9fbfe52e086d1.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\d6b0e189847a4d1293e9fbfe52e086d1.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "147799"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 00:59:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7C+EtpvaB+JITP/bPh6Hrmekms4V02jhufpz+3kKFkc="}]}, {"Route": "uploads/documents/db26e8581e504a6fbe89c9f2c25ea049.e1igdv5m66.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\db26e8581e504a6fbe89c9f2c25ea049.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:30:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1igdv5m66"}, {"Name": "label", "Value": "uploads/documents/db26e8581e504a6fbe89c9f2c25ea049.pdf"}, {"Name": "integrity", "Value": "sha256-UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI="}]}, {"Route": "uploads/documents/db26e8581e504a6fbe89c9f2c25ea049.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\db26e8581e504a6fbe89c9f2c25ea049.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:30:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI="}]}, {"Route": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db.6mabyag1xn.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:32:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6mabyag1xn"}, {"Name": "label", "Value": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db.pdf"}, {"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/e1fa76fc737043cd963851ab3d4f50db.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\e1fa76fc737043cd963851ab3d4f50db.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "48194"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:32:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-gutJxmAixzCz0cRFOg/Rh+oSJmGOnIzJSsRszMHIgzQ="}]}, {"Route": "uploads/documents/ea02708c3aca4feca7c6fddf23666bfe.et9dbzk0w9.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea02708c3aca4feca7c6fddf23666bfe.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25855"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:07:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "et9dbzk0w9"}, {"Name": "label", "Value": "uploads/documents/ea02708c3aca4feca7c6fddf23666bfe.pdf"}, {"Name": "integrity", "Value": "sha256-DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk="}]}, {"Route": "uploads/documents/ea02708c3aca4feca7c6fddf23666bfe.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea02708c3aca4feca7c6fddf23666bfe.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25855"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:07:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk="}]}, {"Route": "uploads/documents/ea1632403571458e8fd9910c56d0f0f5.kob432tvll.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea1632403571458e8fd9910c56d0f0f5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "301140"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"JM+8CsTmG0kHcdIAFCZqjv59Fki8RHEaHqDxVI87feA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 03:27:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kob432tvll"}, {"Name": "label", "Value": "uploads/documents/ea1632403571458e8fd9910c56d0f0f5.pdf"}, {"Name": "integrity", "Value": "sha256-JM+8CsTmG0kHcdIAFCZqjv59Fki8RHEaHqDxVI87feA="}]}, {"Route": "uploads/documents/ea1632403571458e8fd9910c56d0f0f5.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\ea1632403571458e8fd9910c56d0f0f5.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "301140"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"JM+8CsTmG0kHcdIAFCZqjv59Fki8RHEaHqDxVI87feA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 03:27:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JM+8CsTmG0kHcdIAFCZqjv59Fki8RHEaHqDxVI87feA="}]}, {"Route": "uploads/documents/eb65cd3797854633828ab9b66feaa963.gonabiiekc.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\eb65cd3797854633828ab9b66feaa963.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91721"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:34:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gonabiiekc"}, {"Name": "label", "Value": "uploads/documents/eb65cd3797854633828ab9b66feaa963.pdf"}, {"Name": "integrity", "Value": "sha256-YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs="}]}, {"Route": "uploads/documents/eb65cd3797854633828ab9b66feaa963.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\eb65cd3797854633828ab9b66feaa963.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "91721"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:34:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YKlbT8Wcbq9KmUm1P6jTq+35i+huGe5jsVc0MJSgFWs="}]}, {"Route": "uploads/documents/f3aa1eeb1a4a43f39ee2622a9c538fff.et9dbzk0w9.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25855"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:49:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "et9dbzk0w9"}, {"Name": "label", "Value": "uploads/documents/f3aa1eeb1a4a43f39ee2622a9c538fff.pdf"}, {"Name": "integrity", "Value": "sha256-DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk="}]}, {"Route": "uploads/documents/f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f3aa1eeb1a4a43f39ee2622a9c538fff.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25855"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:49:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-DvbWDQdvxAjzuZ67onSQ5PKivTJf/0oEW0kAs8il7Vk="}]}, {"Route": "uploads/documents/f5a02559328b46e78d0360bce78e29a6.e1igdv5m66.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f5a02559328b46e78d0360bce78e29a6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 22:22:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "e1igdv5m66"}, {"Name": "label", "Value": "uploads/documents/f5a02559328b46e78d0360bce78e29a6.pdf"}, {"Name": "integrity", "Value": "sha256-UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI="}]}, {"Route": "uploads/documents/f5a02559328b46e78d0360bce78e29a6.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\f5a02559328b46e78d0360bce78e29a6.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2154"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 22:22:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UpiF/8p1ZsgW3ZPJtCxkX+ZGAfODw0oTSRilo9nUYAI="}]}, {"Route": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012.jilall62b3.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63019"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:04:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jilall62b3"}, {"Name": "label", "Value": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012.pdf"}, {"Name": "integrity", "Value": "sha256-axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM="}]}, {"Route": "uploads/documents/fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\documents\\fa8afc1d2d664eb2a6c2057eb6a30012.pdf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63019"}, {"Name": "Content-Type", "Value": "application/pdf"}, {"Name": "ETag", "Value": "\"axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:04:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-axSfQ4GEfDpg+365QvDt55vNqdPCE6fwB6NRIU/0hOM="}]}, {"Route": "uploads/images/012f27a40a4b47e6ae5db437acc75f99.bm200vq19x.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\012f27a40a4b47e6ae5db437acc75f99.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51973"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:06:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bm200vq19x"}, {"Name": "label", "Value": "uploads/images/012f27a40a4b47e6ae5db437acc75f99.png"}, {"Name": "integrity", "Value": "sha256-Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o="}]}, {"Route": "uploads/images/012f27a40a4b47e6ae5db437acc75f99.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\012f27a40a4b47e6ae5db437acc75f99.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51973"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:06:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o="}]}, {"Route": "uploads/images/058264df89934078a8fc82a563396495.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\058264df89934078a8fc82a563396495.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:54:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/058264df89934078a8fc82a563396495.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\058264df89934078a8fc82a563396495.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:54:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/058264df89934078a8fc82a563396495.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/0927b9a8ae0b4ab991bd710ade619ee2.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\0927b9a8ae0b4ab991bd710ade619ee2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 22:16:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/0927b9a8ae0b4ab991bd710ade619ee2.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\0927b9a8ae0b4ab991bd710ade619ee2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 22:16:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/0927b9a8ae0b4ab991bd710ade619ee2.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/09adcd0d2d104d32ac9533b83026a8d1.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\09adcd0d2d104d32ac9533b83026a8d1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 21:10:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/09adcd0d2d104d32ac9533b83026a8d1.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\09adcd0d2d104d32ac9533b83026a8d1.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 21:10:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/09adcd0d2d104d32ac9533b83026a8d1.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/10052d090a244fe28441690bd549cf4f.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\10052d090a244fe28441690bd549cf4f.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:49:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/10052d090a244fe28441690bd549cf4f.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/10052d090a244fe28441690bd549cf4f.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\10052d090a244fe28441690bd549cf4f.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:49:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/158c4d49e0444e039ee77f372cb74346.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\158c4d49e0444e039ee77f372cb74346.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:42:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/158c4d49e0444e039ee77f372cb74346.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/158c4d49e0444e039ee77f372cb74346.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\158c4d49e0444e039ee77f372cb74346.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:42:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/16cbcd9c40c848068b1303ef10915a19.npx0pid61y.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\16cbcd9c40c848068b1303ef10915a19.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "890"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qztz0HhubvxF0ZyaTYkeNdm1yfNPP85GMBMjocWues0=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:43:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "npx0pid61y"}, {"Name": "label", "Value": "uploads/images/16cbcd9c40c848068b1303ef10915a19.png"}, {"Name": "integrity", "Value": "sha256-qztz0HhubvxF0ZyaTYkeNdm1yfNPP85GMBMjocWues0="}]}, {"Route": "uploads/images/16cbcd9c40c848068b1303ef10915a19.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\16cbcd9c40c848068b1303ef10915a19.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "890"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qztz0HhubvxF0ZyaTYkeNdm1yfNPP85GMBMjocWues0=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:43:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qztz0HhubvxF0ZyaTYkeNdm1yfNPP85GMBMjocWues0="}]}, {"Route": "uploads/images/1870288851874090b203bf27c9321fed.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:55:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/1870288851874090b203bf27c9321fed.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/1870288851874090b203bf27c9321fed.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\1870288851874090b203bf27c9321fed.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:55:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/27cdd4e56358437d960e875392a5c2b1.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\27cdd4e56358437d960e875392a5c2b1.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 15:19:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/27cdd4e56358437d960e875392a5c2b1.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/27cdd4e56358437d960e875392a5c2b1.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\27cdd4e56358437d960e875392a5c2b1.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 15:19:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/312205ccd18f4650bef24653fbf9f64a.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:45:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/312205ccd18f4650bef24653fbf9f64a.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/312205ccd18f4650bef24653fbf9f64a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\312205ccd18f4650bef24653fbf9f64a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:45:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/314d69b11b25437fa3e677cb0bc0df70.42qs53m74g.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314d69b11b25437fa3e677cb0bc0df70.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:42:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42qs53m74g"}, {"Name": "label", "Value": "uploads/images/314d69b11b25437fa3e677cb0bc0df70.png"}, {"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/314d69b11b25437fa3e677cb0bc0df70.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314d69b11b25437fa3e677cb0bc0df70.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:42:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/314ee81838e24ff69f9376e5ad555018.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314ee81838e24ff69f9376e5ad555018.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:03:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/314ee81838e24ff69f9376e5ad555018.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\314ee81838e24ff69f9376e5ad555018.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 16:03:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/314ee81838e24ff69f9376e5ad555018.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/338974a63b324a6f8e140174a2be9358.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\338974a63b324a6f8e140174a2be9358.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 15:32:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/338974a63b324a6f8e140174a2be9358.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/338974a63b324a6f8e140174a2be9358.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\338974a63b324a6f8e140174a2be9358.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 15:32:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/34222e73c2af41da84d4996eb1794010.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34222e73c2af41da84d4996eb1794010.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Jul 2025 23:47:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/34222e73c2af41da84d4996eb1794010.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34222e73c2af41da84d4996eb1794010.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Jul 2025 23:47:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/34222e73c2af41da84d4996eb1794010.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/34a889f3e0ce40e085d25f74812e870b.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34a889f3e0ce40e085d25f74812e870b.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 03:37:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/34a889f3e0ce40e085d25f74812e870b.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/34a889f3e0ce40e085d25f74812e870b.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\34a889f3e0ce40e085d25f74812e870b.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 03:37:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/383b2c9c091843a698a7f649130db384.c40i8h3quo.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\383b2c9c091843a698a7f649130db384.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70779"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HG0sEXWwXhhOpCV7Bk43ZIow61iO8OfJhFf0De0lYvc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Jul 2025 02:42:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c40i8h3quo"}, {"Name": "label", "Value": "uploads/images/383b2c9c091843a698a7f649130db384.png"}, {"Name": "integrity", "Value": "sha256-HG0sEXWwXhhOpCV7Bk43ZIow61iO8OfJhFf0De0lYvc="}]}, {"Route": "uploads/images/383b2c9c091843a698a7f649130db384.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\383b2c9c091843a698a7f649130db384.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70779"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"HG0sEXWwXhhOpCV7Bk43ZIow61iO8OfJhFf0De0lYvc=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Jul 2025 02:42:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HG0sEXWwXhhOpCV7Bk43ZIow61iO8OfJhFf0De0lYvc="}]}, {"Route": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f.jj02sd2q5h.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:18:40 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj02sd2q5h"}, {"Name": "label", "Value": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f.png"}, {"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/3a0ace1c93e946709f42cbfb2279e65f.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\3a0ace1c93e946709f42cbfb2279e65f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:18:40 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/43ff42ee0f494b1fac40397ff6cb35e4.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\43ff42ee0f494b1fac40397ff6cb35e4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:32:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/43ff42ee0f494b1fac40397ff6cb35e4.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\43ff42ee0f494b1fac40397ff6cb35e4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:32:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/43ff42ee0f494b1fac40397ff6cb35e4.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14596"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s="}]}, {"Route": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec.qt5iiuu92c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4886e04ad10545cfa694a3d3f06d85ec.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14596"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qt5iiuu92c"}, {"Name": "label", "Value": "uploads/images/4886e04ad10545cfa694a3d3f06d85ec.png"}, {"Name": "integrity", "Value": "sha256-/pffW2qzqA9CBtmme640Xn8tkV+gYDbxpODuhXHgE+s="}]}, {"Route": "uploads/images/48d3116a39f34661b105e97351e9dd65.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\48d3116a39f34661b105e97351e9dd65.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:23:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/48d3116a39f34661b105e97351e9dd65.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/48d3116a39f34661b105e97351e9dd65.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\48d3116a39f34661b105e97351e9dd65.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 01:23:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4.160dqoac5z.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:13:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "160dqoac5z"}, {"Name": "label", "Value": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4.png"}, {"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/4c2fcb8d51f0455fad91d5629bc65ef4.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4c2fcb8d51f0455fad91d5629bc65ef4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:13:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/4ca45322624947dfa4c15343a522edaa.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:05:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/4ca45322624947dfa4c15343a522edaa.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/4ca45322624947dfa4c15343a522edaa.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4ca45322624947dfa4c15343a522edaa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:05:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/4fa649b91a96437e8b2da7b488ff9c72.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4fa649b91a96437e8b2da7b488ff9c72.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 15:37:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/4fa649b91a96437e8b2da7b488ff9c72.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\4fa649b91a96437e8b2da7b488ff9c72.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Sun, 20 Jul 2025 15:37:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/4fa649b91a96437e8b2da7b488ff9c72.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/5094f812e6634ab4acc07ed00767abd5.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5094f812e6634ab4acc07ed00767abd5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32278"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 21:21:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g<PERSON><PERSON>dczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI="}]}, {"Route": "uploads/images/5094f812e6634ab4acc07ed00767abd5.qm5cpjs4gn.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5094f812e6634ab4acc07ed00767abd5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32278"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 21:21:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qm5cpjs4gn"}, {"Name": "label", "Value": "uploads/images/5094f812e6634ab4acc07ed00767abd5.png"}, {"Name": "integrity", "Value": "sha256-g<PERSON><PERSON>dczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI="}]}, {"Route": "uploads/images/53e82cd4346e4263bc415d38312d7b49.csvf1bo9y0.jpg", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "158471"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:43:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "csvf1bo9y0"}, {"Name": "label", "Value": "uploads/images/53e82cd4346e4263bc415d38312d7b49.jpg"}, {"Name": "integrity", "Value": "sha256-VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10="}]}, {"Route": "uploads/images/53e82cd4346e4263bc415d38312d7b49.jpg", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\53e82cd4346e4263bc415d38312d7b49.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "158471"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:43:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VEQKFnXTwXCWL0eSgISkz4zFS8E4pb7n1re/Jm6cR10="}]}, {"Route": "uploads/images/59d9b375bfc749ee94270d0724e0d922.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59d9b375bfc749ee94270d0724e0d922.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 17:14:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/59d9b375bfc749ee94270d0724e0d922.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/59d9b375bfc749ee94270d0724e0d922.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59d9b375bfc749ee94270d0724e0d922.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 17:14:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/59e3e01493f748bfb81c21759fbe6502.mi7mdsl38n.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59e3e01493f748bfb81c21759fbe6502.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "542436"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cgPuaqI2ICuLaq72Z4vc717DOoL+YLIVdE6EK57AIMQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 18:27:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mi7mdsl38n"}, {"Name": "label", "Value": "uploads/images/59e3e01493f748bfb81c21759fbe6502.png"}, {"Name": "integrity", "Value": "sha256-cgPuaqI2ICuLaq72Z4vc717DOoL+YLIVdE6EK57AIMQ="}]}, {"Route": "uploads/images/59e3e01493f748bfb81c21759fbe6502.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\59e3e01493f748bfb81c21759fbe6502.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "542436"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cgPuaqI2ICuLaq72Z4vc717DOoL+YLIVdE6EK57AIMQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 18:27:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cgPuaqI2ICuLaq72Z4vc717DOoL+YLIVdE6EK57AIMQ="}]}, {"Route": "uploads/images/5f111548ca9e4c5cb05845290f0b0eb5.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f111548ca9e4c5cb05845290f0b0eb5.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:51:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/5f111548ca9e4c5cb05845290f0b0eb5.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/5f111548ca9e4c5cb05845290f0b0eb5.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f111548ca9e4c5cb05845290f0b0eb5.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:51:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/5f82d034f5fc408991373eec12a04772.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f82d034f5fc408991373eec12a04772.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 04:23:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/images/5f82d034f5fc408991373eec12a04772.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/5f82d034f5fc408991373eec12a04772.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f82d034f5fc408991373eec12a04772.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 04:23:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/5f8765d1b6124a7c95a2923a03bbe7ab.42qs53m74g.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f8765d1b6124a7c95a2923a03bbe7ab.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 00:43:40 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42qs53m74g"}, {"Name": "label", "Value": "uploads/images/5f8765d1b6124a7c95a2923a03bbe7ab.png"}, {"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/5f8765d1b6124a7c95a2923a03bbe7ab.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f8765d1b6124a7c95a2923a03bbe7ab.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 00:43:40 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/5f98bb1a22fc476f974000194d3ee208.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f98bb1a22fc476f974000194d3ee208.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:01:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/5f98bb1a22fc476f974000194d3ee208.umutyta48r.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\5f98bb1a22fc476f974000194d3ee208.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:01:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umutyta48r"}, {"Name": "label", "Value": "uploads/images/5f98bb1a22fc476f974000194d3ee208.png"}, {"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/610efb0bb9a745baa5a60fbe8b50789a.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\610efb0bb9a745baa5a60fbe8b50789a.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/621c79053943489d9838496dccd18b32.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\621c79053943489d9838496dccd18b32.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 22:11:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/621c79053943489d9838496dccd18b32.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\621c79053943489d9838496dccd18b32.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 22:11:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/621c79053943489d9838496dccd18b32.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:23:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/631a190e6e0e4858a805e2bf75d6d33a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\631a190e6e0e4858a805e2bf75d6d33a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:23:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/66ca76d3dec94b7fa33213be976243b2.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\66ca76d3dec94b7fa33213be976243b2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:54:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/66ca76d3dec94b7fa33213be976243b2.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/66ca76d3dec94b7fa33213be976243b2.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\66ca76d3dec94b7fa33213be976243b2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:54:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/69b88efe4c09475aba9ea7025d4e346f.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\69b88efe4c09475aba9ea7025d4e346f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:48:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/69b88efe4c09475aba9ea7025d4e346f.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/69b88efe4c09475aba9ea7025d4e346f.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\69b88efe4c09475aba9ea7025d4e346f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 23:48:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/6a008f012f2b46a9a77ddbed720921cf.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6a008f012f2b46a9a77ddbed720921cf.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 21:57:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/6a008f012f2b46a9a77ddbed720921cf.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6a008f012f2b46a9a77ddbed720921cf.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 21:57:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/6a008f012f2b46a9a77ddbed720921cf.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:50:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/6d796a2e432c48e3b57b61a7cc64e85c.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d796a2e432c48e3b57b61a7cc64e85c.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:50:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/6d8d0634bb084b3380094f266ed72df7.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d8d0634bb084b3380094f266ed72df7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:30:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/6d8d0634bb084b3380094f266ed72df7.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/6d8d0634bb084b3380094f266ed72df7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\6d8d0634bb084b3380094f266ed72df7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 22:30:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc.1nfn82ehfv.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1345158"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:30:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1nfn82ehfv"}, {"Name": "label", "Value": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc.png"}, {"Name": "integrity", "Value": "sha256-E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM="}]}, {"Route": "uploads/images/7054f84a3f8f4a409e0e9290c878fbcc.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\7054f84a3f8f4a409e0e9290c878fbcc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1345158"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:30:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-E+GMZQsbl/qQn7wQJyD/nl/muj/Q8tsck4DYoiixGnM="}]}, {"Route": "uploads/images/713c55958e434619b8dbd17a5ef62b7e.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:21:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/713c55958e434619b8dbd17a5ef62b7e.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/713c55958e434619b8dbd17a5ef62b7e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\713c55958e434619b8dbd17a5ef62b7e.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:21:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/73ef279aa84b42e1a830b0c1747a9baa.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\73ef279aa84b42e1a830b0c1747a9baa.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 03:11:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/73ef279aa84b42e1a830b0c1747a9baa.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/73ef279aa84b42e1a830b0c1747a9baa.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\73ef279aa84b42e1a830b0c1747a9baa.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 03:11:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/757730977ffe43dbbdef2c17e557a5cb.gs16logzrz.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\757730977ffe43dbbdef2c17e557a5cb.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "65595"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"kKNYPA2xO19s3COMjoENuFzDJDboPkVRPBOvK78B+lA=\""}, {"Name": "Last-Modified", "Value": "Sat, 26 Jul 2025 19:17:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gs16logzrz"}, {"Name": "label", "Value": "uploads/images/757730977ffe43dbbdef2c17e557a5cb.png"}, {"Name": "integrity", "Value": "sha256-kKNYPA2xO19s3COMjoENuFzDJDboPkVRPBOvK78B+lA="}]}, {"Route": "uploads/images/757730977ffe43dbbdef2c17e557a5cb.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\757730977ffe43dbbdef2c17e557a5cb.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "65595"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"kKNYPA2xO19s3COMjoENuFzDJDboPkVRPBOvK78B+lA=\""}, {"Name": "Last-Modified", "Value": "Sat, 26 Jul 2025 19:17:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kKNYPA2xO19s3COMjoENuFzDJDboPkVRPBOvK78B+lA="}]}, {"Route": "uploads/images/771992cb22384d97bd1f5d0718a5bac6.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\771992cb22384d97bd1f5d0718a5bac6.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:03:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/771992cb22384d97bd1f5d0718a5bac6.umutyta48r.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\771992cb22384d97bd1f5d0718a5bac6.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:03:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umutyta48r"}, {"Name": "label", "Value": "uploads/images/771992cb22384d97bd1f5d0718a5bac6.png"}, {"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/783de52104b84aac9fff70eb4857a083.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\783de52104b84aac9fff70eb4857a083.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:32:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/783de52104b84aac9fff70eb4857a083.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/783de52104b84aac9fff70eb4857a083.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\783de52104b84aac9fff70eb4857a083.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:32:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/8422115944564f0d9b5336c152d37758.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:04:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/8422115944564f0d9b5336c152d37758.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8422115944564f0d9b5336c152d37758.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:04:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/8422115944564f0d9b5336c152d37758.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/866b5614a13843f5a8c082332a886e8d.bhip2kqmjj.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\866b5614a13843f5a8c082332a886e8d.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:04:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bhip2kqmjj"}, {"Name": "label", "Value": "uploads/images/866b5614a13843f5a8c082332a886e8d.png"}, {"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/866b5614a13843f5a8c082332a886e8d.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\866b5614a13843f5a8c082332a886e8d.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:04:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/86f9084a89de49dcb333d63c0bf993b9.3oz33zfofh.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87614"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3oz33zfofh"}, {"Name": "label", "Value": "uploads/images/86f9084a89de49dcb333d63c0bf993b9.webp"}, {"Name": "integrity", "Value": "sha256-UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw="}]}, {"Route": "uploads/images/86f9084a89de49dcb333d63c0bf993b9.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\86f9084a89de49dcb333d63c0bf993b9.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87614"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UxYff5Z/UZ8/0B2kpJToiiF7lnlcm5vqlH7G0bOO6Bw="}]}, {"Route": "uploads/images/87959591ff1d4bb9b6d3cf9617456555.45mp2a8mvz.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:16:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "45mp2a8mvz"}, {"Name": "label", "Value": "uploads/images/87959591ff1d4bb9b6d3cf9617456555.png"}, {"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/87959591ff1d4bb9b6d3cf9617456555.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\87959591ff1d4bb9b6d3cf9617456555.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:16:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/8891458a31864b6a92594c256956cc0f.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8891458a31864b6a92594c256956cc0f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:01:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/8891458a31864b6a92594c256956cc0f.umutyta48r.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8891458a31864b6a92594c256956cc0f.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:01:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umutyta48r"}, {"Name": "label", "Value": "uploads/images/8891458a31864b6a92594c256956cc0f.png"}, {"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:36:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/8e4b91c5d9284cea9b84c8b70c264c79.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e4b91c5d9284cea9b84c8b70c264c79.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:36:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/8e75782283de4a1ab7da0396404ac48c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e75782283de4a1ab7da0396404ac48c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:58:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/8e75782283de4a1ab7da0396404ac48c.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8e75782283de4a1ab7da0396404ac48c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Fri, 11 Jul 2025 21:58:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/8e75782283de4a1ab7da0396404ac48c.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/8f38d922b2a6453e8d9df2beb9012c53.bhip2kqmjj.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8f38d922b2a6453e8d9df2beb9012c53.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:01:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bhip2kqmjj"}, {"Name": "label", "Value": "uploads/images/8f38d922b2a6453e8d9df2beb9012c53.png"}, {"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/8f38d922b2a6453e8d9df2beb9012c53.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\8f38d922b2a6453e8d9df2beb9012c53.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 23:01:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/90fda8458bb9455ea6069717e3093d03.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\90fda8458bb9455ea6069717e3093d03.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "<PERSON>i, 11 Jul 2025 23:42:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/90fda8458bb9455ea6069717e3093d03.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/90fda8458bb9455ea6069717e3093d03.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\90fda8458bb9455ea6069717e3093d03.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "<PERSON>i, 11 Jul 2025 23:42:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/9130a0bd7c4b4a3599d408a4fe24745a.bhip2kqmjj.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9130a0bd7c4b4a3599d408a4fe24745a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 21:11:58 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bhip2kqmjj"}, {"Name": "label", "Value": "uploads/images/9130a0bd7c4b4a3599d408a4fe24745a.png"}, {"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/9130a0bd7c4b4a3599d408a4fe24745a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9130a0bd7c4b4a3599d408a4fe24745a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 21:11:58 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/97b49fcfbb844432a4b311cb199e86f9.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:54:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/97b49fcfbb844432a4b311cb199e86f9.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/97b49fcfbb844432a4b311cb199e86f9.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\97b49fcfbb844432a4b311cb199e86f9.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:54:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/9854cf13659b4bb6a2141d0824af4666.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:07:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/9854cf13659b4bb6a2141d0824af4666.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/9854cf13659b4bb6a2141d0824af4666.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9854cf13659b4bb6a2141d0824af4666.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:07:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/98a77b5c42654bf6b3f376aa6860eed5.42qs53m74g.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\98a77b5c42654bf6b3f376aa6860eed5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 00:47:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42qs53m74g"}, {"Name": "label", "Value": "uploads/images/98a77b5c42654bf6b3f376aa6860eed5.png"}, {"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/98a77b5c42654bf6b3f376aa6860eed5.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\98a77b5c42654bf6b3f376aa6860eed5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 00:47:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/9a342b7e0e404095a917be1fc40364ae.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:50:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/9a342b7e0e404095a917be1fc40364ae.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a342b7e0e404095a917be1fc40364ae.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:50:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/9a342b7e0e404095a917be1fc40364ae.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/9a7cecf2a2af4dc88583fc5a439133cf.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a7cecf2a2af4dc88583fc5a439133cf.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 22:28:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/9a7cecf2a2af4dc88583fc5a439133cf.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/9a7cecf2a2af4dc88583fc5a439133cf.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9a7cecf2a2af4dc88583fc5a439133cf.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Wed, 09 Jul 2025 22:28:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/9ac777271fb344708f6133a9f76a4bf5.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9ac777271fb344708f6133a9f76a4bf5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:05:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/9ac777271fb344708f6133a9f76a4bf5.umutyta48r.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9ac777271fb344708f6133a9f76a4bf5.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:05:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umutyta48r"}, {"Name": "label", "Value": "uploads/images/9ac777271fb344708f6133a9f76a4bf5.png"}, {"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/9c71ea3b963b479e9df40428b62504be.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9c71ea3b963b479e9df40428b62504be.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 18:33:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/9c71ea3b963b479e9df40428b62504be.t3dbuv7mfy.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9c71ea3b963b479e9df40428b62504be.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 18:33:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t3dbuv7mfy"}, {"Name": "label", "Value": "uploads/images/9c71ea3b963b479e9df40428b62504be.png"}, {"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155911"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI="}]}, {"Route": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c.r7rmd0638a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\9df790c0db6d4d7e9f302edc88b5804c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "155911"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:15:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r7rmd0638a"}, {"Name": "label", "Value": "uploads/images/9df790c0db6d4d7e9f302edc88b5804c.png"}, {"Name": "integrity", "Value": "sha256-uX8tYvCPYY6gXw26SuClSH9rciWhnx9t0oz+T27ZDSI="}]}, {"Route": "uploads/images/a64a54a1298e4b8c81bf46d705996980.160dqoac5z.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:31:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "160dqoac5z"}, {"Name": "label", "Value": "uploads/images/a64a54a1298e4b8c81bf46d705996980.png"}, {"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/a64a54a1298e4b8c81bf46d705996980.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\a64a54a1298e4b8c81bf46d705996980.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "853270"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 13:31:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-52Oa6P2DToTBO8LaH35EDZ/Ez/Hb2ZEjhbM/+dpRNYY="}]}, {"Route": "uploads/images/aa469b655153465e9cd1265d5a36a8dd.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:41:59 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/aa469b655153465e9cd1265d5a36a8dd.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa469b655153465e9cd1265d5a36a8dd.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:41:59 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/aa469b655153465e9cd1265d5a36a8dd.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/aa812b7960134dd380c2c5fcc1286289.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:31:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/aa812b7960134dd380c2c5fcc1286289.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/aa812b7960134dd380c2c5fcc1286289.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aa812b7960134dd380c2c5fcc1286289.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:31:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/ae1bc4df202d4ee7ad0662c86c040945.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ae1bc4df202d4ee7ad0662c86c040945.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32278"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:48:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g<PERSON><PERSON>dczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI="}]}, {"Route": "uploads/images/ae1bc4df202d4ee7ad0662c86c040945.qm5cpjs4gn.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ae1bc4df202d4ee7ad0662c86c040945.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32278"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:48:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qm5cpjs4gn"}, {"Name": "label", "Value": "uploads/images/ae1bc4df202d4ee7ad0662c86c040945.png"}, {"Name": "integrity", "Value": "sha256-g<PERSON><PERSON>dczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI="}]}, {"Route": "uploads/images/aee04329d556487dbb70eccf248638db.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:48:56 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/aee04329d556487dbb70eccf248638db.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\aee04329d556487dbb70eccf248638db.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Thu, 03 Jul 2025 01:48:56 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/aee04329d556487dbb70eccf248638db.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/b3ac325587274e74a2f539ae88f27bf0.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3ac325587274e74a2f539ae88f27bf0.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 22:08:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/b3ac325587274e74a2f539ae88f27bf0.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/b3ac325587274e74a2f539ae88f27bf0.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3ac325587274e74a2f539ae88f27bf0.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 22:08:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/b3c8c8f821c4448f958edb509d8ca866.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3c8c8f821c4448f958edb509d8ca866.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:07:26 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/b3c8c8f821c4448f958edb509d8ca866.t3dbuv7mfy.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b3c8c8f821c4448f958edb509d8ca866.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:07:26 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t3dbuv7mfy"}, {"Name": "label", "Value": "uploads/images/b3c8c8f821c4448f958edb509d8ca866.png"}, {"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/b54042592d954bf29ca490c559a2c12b.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b54042592d954bf29ca490c559a2c12b.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 14:48:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/b54042592d954bf29ca490c559a2c12b.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/b54042592d954bf29ca490c559a2c12b.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\b54042592d954bf29ca490c559a2c12b.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 14:48:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/bd28a12d15e24fb48511eb051459a6bc.ia1swkvltk.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:07:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ia1swkvltk"}, {"Name": "label", "Value": "uploads/images/bd28a12d15e24fb48511eb051459a6bc.png"}, {"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/bd28a12d15e24fb48511eb051459a6bc.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\bd28a12d15e24fb48511eb051459a6bc.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "885730"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 16:07:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-duq3FZRvd3vSLnQWk+vTSt1NCGopAlHIH8aU8JUElc8="}]}, {"Route": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87.lb3ihrmur6.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "62981"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lb3ihrmur6"}, {"Name": "label", "Value": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87.png"}, {"Name": "integrity", "Value": "sha256-r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw="}]}, {"Route": "uploads/images/c12085ba2bf34fb09f79c7850e7a6e87.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c12085ba2bf34fb09f79c7850e7a6e87.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "62981"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:16:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r0HmmgXg80L5bGekrx0SHvXKeEOeDQvth5XrNkEGbDw="}]}, {"Route": "uploads/images/c2e7dc6ac9f743a4b5443b55463b3766.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c2e7dc6ac9f743a4b5443b55463b3766.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "66359"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"PERl6HpxIJLpQ9Lch2j8XLS2/Y4kw8lhv7j40ENBi2I=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:02:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PERl6HpxIJLpQ9Lch2j8XLS2/Y4kw8lhv7j40ENBi2I="}]}, {"Route": "uploads/images/c2e7dc6ac9f743a4b5443b55463b3766.sejuu0i0l0.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c2e7dc6ac9f743a4b5443b55463b3766.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "66359"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"PERl6HpxIJLpQ9Lch2j8XLS2/Y4kw8lhv7j40ENBi2I=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:02:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sejuu0i0l0"}, {"Name": "label", "Value": "uploads/images/c2e7dc6ac9f743a4b5443b55463b3766.png"}, {"Name": "integrity", "Value": "sha256-PERl6HpxIJLpQ9Lch2j8XLS2/Y4kw8lhv7j40ENBi2I="}]}, {"Route": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c.ljkl4o8aee.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3582183"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:33:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ljkl4o8aee"}, {"Name": "label", "Value": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c.png"}, {"Name": "integrity", "Value": "sha256-v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U="}]}, {"Route": "uploads/images/c3cf257303cd47dfa6018f1b87bdae6c.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c3cf257303cd47dfa6018f1b87bdae6c.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3582183"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 18:33:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v8CNdUHekPmKC+kXCvGU9mz/SMbNlbVn/2Cx0uizk9U="}]}, {"Route": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:51 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/c4be6ded3cb74eeeb91a53dd8f79d074.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c4be6ded3cb74eeeb91a53dd8f79d074.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:51 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/c835a2f77dc6491195963a76f091b3d8.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c835a2f77dc6491195963a76f091b3d8.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 22:21:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/c835a2f77dc6491195963a76f091b3d8.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/c835a2f77dc6491195963a76f091b3d8.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\c835a2f77dc6491195963a76f091b3d8.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 22:21:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/ca0161389fa346e6be985efb101cf3f7.45mp2a8mvz.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:28:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "45mp2a8mvz"}, {"Name": "label", "Value": "uploads/images/ca0161389fa346e6be985efb101cf3f7.png"}, {"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/ca0161389fa346e6be985efb101cf3f7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ca0161389fa346e6be985efb101cf3f7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "659876"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 15:28:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qBmDm8T4QaftiVzlhIA0uT+wdWrw6rn1MEAx8WEsFfg="}]}, {"Route": "uploads/images/d0f43a0c17dd4ae78ab837ce42692072.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d0f43a0c17dd4ae78ab837ce42692072.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 21:12:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/d0f43a0c17dd4ae78ab837ce42692072.t3dbuv7mfy.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d0f43a0c17dd4ae78ab837ce42692072.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 21:12:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t3dbuv7mfy"}, {"Name": "label", "Value": "uploads/images/d0f43a0c17dd4ae78ab837ce42692072.png"}, {"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/d277b7275c644e738414f5c2bc40bc99.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d277b7275c644e738414f5c2bc40bc99.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 03:34:52 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/d277b7275c644e738414f5c2bc40bc99.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d277b7275c644e738414f5c2bc40bc99.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 08 Jul 2025 03:34:52 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/d277b7275c644e738414f5c2bc40bc99.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46057"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 21:21:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc="}]}, {"Route": "uploads/images/d36bc059c16b44bcbc0a35050a0a0b5e.vcjj9guxre.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d36bc059c16b44bcbc0a35050a0a0b5e.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46057"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 21:21:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vcjj9guxre"}, {"Name": "label", "Value": "uploads/images/d36bc059c16b44bcbc0a35050a0a0b5e.PNG"}, {"Name": "integrity", "Value": "sha256-v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc="}]}, {"Route": "uploads/images/d5ef1ef2d081452180eec81c36b2b8e9.bhip2kqmjj.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5ef1ef2d081452180eec81c36b2b8e9.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:03:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bhip2kqmjj"}, {"Name": "label", "Value": "uploads/images/d5ef1ef2d081452180eec81c36b2b8e9.png"}, {"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/d5ef1ef2d081452180eec81c36b2b8e9.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5ef1ef2d081452180eec81c36b2b8e9.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "364136"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 19:03:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z1u6R/B723xPiPyCc7rU74Tis28B/rhU65GKgyPXAbY="}]}, {"Route": "uploads/images/d5effe60e3e84110858bb6b1629056c4.42qs53m74g.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5effe60e3e84110858bb6b1629056c4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Jul 2025 23:57:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42qs53m74g"}, {"Name": "label", "Value": "uploads/images/d5effe60e3e84110858bb6b1629056c4.png"}, {"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/d5effe60e3e84110858bb6b1629056c4.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d5effe60e3e84110858bb6b1629056c4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Jul 2025 23:57:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/d9e709f5e5be4ee4afcff29995499f15.42qs53m74g.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d9e709f5e5be4ee4afcff29995499f15.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:41:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42qs53m74g"}, {"Name": "label", "Value": "uploads/images/d9e709f5e5be4ee4afcff29995499f15.png"}, {"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/d9e709f5e5be4ee4afcff29995499f15.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\d9e709f5e5be4ee4afcff29995499f15.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 02:41:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/db5e4060478d44bdbd0eda3592b741c4.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/db5e4060478d44bdbd0eda3592b741c4.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\db5e4060478d44bdbd0eda3592b741c4.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Sun, 29 Jun 2025 01:16:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/db5e4060478d44bdbd0eda3592b741c4.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/dd0c100c1a6e43efbee4ea0708b5eefa.evylwqip3e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\dd0c100c1a6e43efbee4ea0708b5eefa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Jul 2025 02:42:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "evylwqip3e"}, {"Name": "label", "Value": "uploads/images/dd0c100c1a6e43efbee4ea0708b5eefa.png"}, {"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/dd0c100c1a6e43efbee4ea0708b5eefa.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\dd0c100c1a6e43efbee4ea0708b5eefa.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "86139"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0=\""}, {"Name": "Last-Modified", "Value": "Sun, 27 Jul 2025 02:42:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NjQpDQbn9UeixFSUnKAflBtyuoD4Ot5rXYQAT8vgfK0="}]}, {"Route": "uploads/images/de20a39284b94cc0b97bb816eb1ac085.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\de20a39284b94cc0b97bb816eb1ac085.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 02:34:23 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/de20a39284b94cc0b97bb816eb1ac085.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\de20a39284b94cc0b97bb816eb1ac085.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 02:34:23 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/de20a39284b94cc0b97bb816eb1ac085.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/deef804fae0c4fa08e7b1b901925419a.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\deef804fae0c4fa08e7b1b901925419a.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 23:45:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/deef804fae0c4fa08e7b1b901925419a.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/deef804fae0c4fa08e7b1b901925419a.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\deef804fae0c4fa08e7b1b901925419a.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 23:45:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/e7ddc387739a443ab47f3c0d18e7cab2.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e7ddc387739a443ab47f3c0d18e7cab2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 18:36:40 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/e7ddc387739a443ab47f3c0d18e7cab2.umutyta48r.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e7ddc387739a443ab47f3c0d18e7cab2.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Mon, 28 Jul 2025 18:36:40 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umutyta48r"}, {"Name": "label", "Value": "uploads/images/e7ddc387739a443ab47f3c0d18e7cab2.png"}, {"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/images/e97afe0fea7d46fa90ae97941b717777.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:51:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/e97afe0fea7d46fa90ae97941b717777.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\e97afe0fea7d46fa90ae97941b717777.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 14:51:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/e97afe0fea7d46fa90ae97941b717777.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98.bm200vq19x.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51973"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:25:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bm200vq19x"}, {"Name": "label", "Value": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98.png"}, {"Name": "integrity", "Value": "sha256-Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o="}]}, {"Route": "uploads/images/eb79cd51dc3d43dbbda06c629027ec98.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eb79cd51dc3d43dbbda06c629027ec98.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51973"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:25:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Q8l8GUhamHBK0YbIi2QYsf3keVpACWQScC1PakHwt/o="}]}, {"Route": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d.jj02sd2q5h.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:09:22 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj02sd2q5h"}, {"Name": "label", "Value": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d.png"}, {"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/eec2598549b14c3cb6efa0a6ca5bfe8d.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\eec2598549b14c3cb6efa0a6ca5bfe8d.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1280957"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 24 Jun 2025 14:09:22 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wQB9mCk9PpicYOKXGpuIQC0mLLuKirO2SQ+vB/qwMbU="}]}, {"Route": "uploads/images/f5fa12ce51fd47b39a7341711e685eeb.42qs53m74g.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f5fa12ce51fd47b39a7341711e685eeb.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 00:46:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "42qs53m74g"}, {"Name": "label", "Value": "uploads/images/f5fa12ce51fd47b39a7341711e685eeb.png"}, {"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/f5fa12ce51fd47b39a7341711e685eeb.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f5fa12ce51fd47b39a7341711e685eeb.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180218"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw=\""}, {"Name": "Last-Modified", "Value": "Sat, 12 Jul 2025 00:46:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7KNX0/uQtXd5z4/MTAFfpKjGuk5s8SisA4cez5r5/Iw="}]}, {"Route": "uploads/images/f706c5792702492a90e00628f2eb9ba6.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/f706c5792702492a90e00628f2eb9ba6.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/f706c5792702492a90e00628f2eb9ba6.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f706c5792702492a90e00628f2eb9ba6.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 02:38:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/f8a5a58c11b945118610694f03b0007e.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f8a5a58c11b945118610694f03b0007e.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 23:44:54 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/f8a5a58c11b945118610694f03b0007e.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f8a5a58c11b945118610694f03b0007e.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Fri, 18 Jul 2025 23:44:54 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/images/f8a5a58c11b945118610694f03b0007e.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/images/f93ad881856442cd850032c437ad256a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f93ad881856442cd850032c437ad256a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:31:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/f93ad881856442cd850032c437ad256a.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f93ad881856442cd850032c437ad256a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:31:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/f93ad881856442cd850032c437ad256a.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/f97829c0fe164622a2e1eae38e40117a.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/f97829c0fe164622a2e1eae38e40117a.t3dbuv7mfy.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\f97829c0fe164622a2e1eae38e40117a.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "216207"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:48:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "t3dbuv7mfy"}, {"Name": "label", "Value": "uploads/images/f97829c0fe164622a2e1eae38e40117a.png"}, {"Name": "integrity", "Value": "sha256-GcH838F74sMYRZGDHUKT/emduBGDC+GW1yLAVMoCHIc="}]}, {"Route": "uploads/images/fb22d922074c4743951c4e34232de868.eb9ax9ytkh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:58:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "eb9ax9ytkh"}, {"Name": "label", "Value": "uploads/images/fb22d922074c4743951c4e34232de868.png"}, {"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/fb22d922074c4743951c4e34232de868.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fb22d922074c4743951c4e34232de868.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "726022"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 17:58:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Vs3VhNGbpZOyAaLtFxeifa5xHx/q6GQoe8PLBxmrqg4="}]}, {"Route": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:50:37 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7.pnn48fdjlm.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\fcbd8c0981174ebb8f826b95d2ac61c7.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "196383"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY=\""}, {"Name": "Last-Modified", "Value": "Mon, 23 Jun 2025 15:50:37 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pnn48fdjlm"}, {"Name": "label", "Value": "uploads/images/fcbd8c0981174ebb8f826b95d2ac61c7.png"}, {"Name": "integrity", "Value": "sha256-xYSfZpNDOChSqYB2+/jcLSEOjoGG87EN1FnlWWIQGAY="}]}, {"Route": "uploads/images/feda33a57a2243d2af4b1db8688f2f88.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\feda33a57a2243d2af4b1db8688f2f88.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 15:32:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/images/feda33a57a2243d2af4b1db8688f2f88.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/feda33a57a2243d2af4b1db8688f2f88.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\feda33a57a2243d2af4b1db8688f2f88.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Thu, 17 Jul 2025 15:32:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/images/ff4a6481da70456989622e13b2250a71.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ff4a6481da70456989622e13b2250a71.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:26:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/images/ff4a6481da70456989622e13b2250a71.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/images/ff4a6481da70456989622e13b2250a71.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\images\\ff4a6481da70456989622e13b2250a71.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Sun, 13 Jul 2025 03:26:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32278"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:49:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-g<PERSON><PERSON>dczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI="}]}, {"Route": "uploads/message-attachments/11073ad0-55de-42bb-bc0e-e0a215069961_10.qm5cpjs4gn.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\11073ad0-55de-42bb-bc0e-e0a215069961_10.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "32278"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"gkwudczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:49:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qm5cpjs4gn"}, {"Name": "label", "Value": "uploads/message-attachments/11073ad0-55de-42bb-bc0e-e0a215069961_10.png"}, {"Name": "integrity", "Value": "sha256-g<PERSON><PERSON>dczbAPpJjcWMCJF3O2rZvooObvTqeS9c7uoRjTI="}]}, {"Route": "uploads/message-attachments/4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46057"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:43:57 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc="}]}, {"Route": "uploads/message-attachments/4b03c2c9-c7c4-482c-91d9-964d60405f38_123.vcjj9guxre.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "46057"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:43:57 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vcjj9guxre"}, {"Name": "label", "Value": "uploads/message-attachments/4b03c2c9-c7c4-482c-91d9-964d60405f38_123.PNG"}, {"Name": "integrity", "Value": "sha256-v/ErexDjDMFH3kUDpbyawORn331InLDiWLk20iLlmrc="}]}, {"Route": "uploads/message-attachments/4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:58:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/message-attachments/4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.vfb68j65u8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "195627"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:58:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vfb68j65u8"}, {"Name": "label", "Value": "uploads/message-attachments/4c5539b0-b8cc-4935-8d40-3e40a3de3ad2_TASKS.png"}, {"Name": "integrity", "Value": "sha256-a1MrsLgfosd4HRHc12cWnLhGw8HJ5eBle1AX7jnb2xw="}]}, {"Route": "uploads/message-attachments/63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 20:24:05 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/message-attachments/63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\63102443-cdfe-4a8e-a25c-d183f0fd1b1b_controller.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 20:24:05 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/6da59366-0bed-4102-9711-e5f17991bfb2_controller.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:51:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/message-attachments/6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\6da59366-0bed-4102-9711-e5f17991bfb2_controller.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 19:51:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/b8061978-90a1-4e3d-b474-328770509051_controller.c69f109cb6.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 20:09:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c69f109cb6"}, {"Name": "label", "Value": "uploads/message-attachments/b8061978-90a1-4e3d-b474-328770509051_controller.PNG"}, {"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b8061978-90a1-4e3d-b474-328770509051_controller.PNG", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1011297"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 20:09:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zPKlc1JTCdqbwNbxH6g2myDpJ/lKQqe5Si07AsAJG6g="}]}, {"Route": "uploads/message-attachments/b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.5za9gpxx61.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 20:27:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5za9gpxx61"}, {"Name": "label", "Value": "uploads/message-attachments/b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp"}, {"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/message-attachments/b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\message-attachments\\b86fcdb7-6d0a-4965-ba50-904154d9be04_تقارير صوره نموذدج.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "97050"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag=\""}, {"Name": "Last-Modified", "Value": "Mon, 14 Jul 2025 20:27:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L/xzWvCYyDe3F0vtL4wN2VwwXVf/DxqG4sCPXzAV9Ag="}]}, {"Route": "uploads/profile_images/16_20250716_025728.1756gt4fxa.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\16_20250716_025728.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"M/xRRTChhHnenO7cjk9l7Fz/Swl3F9ZwmyRVYD+06zo=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:57:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1756gt4fxa"}, {"Name": "label", "Value": "uploads/profile_images/16_20250716_025728.png"}, {"Name": "integrity", "Value": "sha256-M/xRRTChhHnenO7cjk9l7Fz/Swl3F9ZwmyRVYD+06zo="}]}, {"Route": "uploads/profile_images/16_20250716_025728.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\16_20250716_025728.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6260"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"M/xRRTChhHnenO7cjk9l7Fz/Swl3F9ZwmyRVYD+06zo=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:57:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-M/xRRTChhHnenO7cjk9l7Fz/Swl3F9ZwmyRVYD+06zo="}]}, {"Route": "uploads/profile_images/17_20250716_020611.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\17_20250716_020611.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:06:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/profile_images/17_20250716_020611.umutyta48r.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\17_20250716_020611.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "235486"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:06:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "umutyta48r"}, {"Name": "label", "Value": "uploads/profile_images/17_20250716_020611.png"}, {"Name": "integrity", "Value": "sha256-VtY0hial1UUl8e9Rde0y4+0MxUADefvzI/667dEq7jw="}]}, {"Route": "uploads/profile_images/18_20250716_025530.9zr8ou7lzf.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\18_20250716_025530.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11549"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"pT8w2MTjFI9Wh4oTtxB0BacHV7dU9BBHBz/i+Rt5J/w=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:55:30 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9zr8ou7lzf"}, {"Name": "label", "Value": "uploads/profile_images/18_20250716_025530.png"}, {"Name": "integrity", "Value": "sha256-pT8w2MTjFI9Wh4oTtxB0BacHV7dU9BBHBz/i+Rt5J/w="}]}, {"Route": "uploads/profile_images/18_20250716_025530.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\18_20250716_025530.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11549"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"pT8w2MTjFI9Wh4oTtxB0BacHV7dU9BBHBz/i+Rt5J/w=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:55:30 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pT8w2MTjFI9Wh4oTtxB0BacHV7dU9BBHBz/i+Rt5J/w="}]}, {"Route": "uploads/profile_images/19_20250716_025433.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\19_20250716_025433.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11348"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"efP1WwguX56r5uUDkiIDBY5KbRnrjWms31lNUZkMpJE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:54:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-efP1WwguX56r5uUDkiIDBY5KbRnrjWms31lNUZkMpJE="}]}, {"Route": "uploads/profile_images/19_20250716_025433.zhkndqnwdu.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\19_20250716_025433.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11348"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"efP1WwguX56r5uUDkiIDBY5KbRnrjWms31lNUZkMpJE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:54:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zhkndqnwdu"}, {"Name": "label", "Value": "uploads/profile_images/19_20250716_025433.png"}, {"Name": "integrity", "Value": "sha256-efP1WwguX56r5uUDkiIDBY5KbRnrjWms31lNUZkMpJE="}]}, {"Route": "uploads/profile_images/20_20250716_011214.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\20_20250716_011214.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30508"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"qSIka5nUZPuD5eWm7tuKkIX7GWOxp7PsLr0ePtrICLQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:12:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qSIka5nUZPuD5eWm7tuKkIX7GWOxp7PsLr0ePtrICLQ="}]}, {"Route": "uploads/profile_images/20_20250716_011214.zl0dp0irm3.webp", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\20_20250716_011214.webp", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "30508"}, {"Name": "Content-Type", "Value": "image/webp"}, {"Name": "ETag", "Value": "\"qSIka5nUZPuD5eWm7tuKkIX7GWOxp7PsLr0ePtrICLQ=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:12:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zl0dp0irm3"}, {"Name": "label", "Value": "uploads/profile_images/20_20250716_011214.webp"}, {"Name": "integrity", "Value": "sha256-qSIka5nUZPuD5eWm7tuKkIX7GWOxp7PsLr0ePtrICLQ="}]}, {"Route": "uploads/profile_images/21_20250715_232513.fago7df5v7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232513.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25896"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:25:13 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fago7df5v7"}, {"Name": "label", "Value": "uploads/profile_images/21_20250715_232513.png"}, {"Name": "integrity", "Value": "sha256-iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg="}]}, {"Route": "uploads/profile_images/21_20250715_232513.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232513.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25896"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:25:13 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg="}]}, {"Route": "uploads/profile_images/21_20250715_232624.fago7df5v7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232624.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25896"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:26:24 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fago7df5v7"}, {"Name": "label", "Value": "uploads/profile_images/21_20250715_232624.png"}, {"Name": "integrity", "Value": "sha256-iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg="}]}, {"Route": "uploads/profile_images/21_20250715_232624.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_232624.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25896"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:26:24 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg="}]}, {"Route": "uploads/profile_images/21_20250715_233121.3nzxj0mxb8.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_233121.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20215"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r8U7ycyf7PZ9VviNH+TH0rSP7i10UNOhHGipCOVfj0Y=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:31:21 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "3nzxj0mxb8"}, {"Name": "label", "Value": "uploads/profile_images/21_20250715_233121.png"}, {"Name": "integrity", "Value": "sha256-r8U7ycyf7PZ9VviNH+TH0rSP7i10UNOhHGipCOVfj0Y="}]}, {"Route": "uploads/profile_images/21_20250715_233121.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_233121.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20215"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"r8U7ycyf7PZ9VviNH+TH0rSP7i10UNOhHGipCOVfj0Y=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:31:21 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-r8U7ycyf7PZ9VviNH+TH0rSP7i10UNOhHGipCOVfj0Y="}]}, {"Route": "uploads/profile_images/21_20250715_234020.9xcst1d5jn.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234020.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29764"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"bSGwTRkG7JJxSWJtpxKk9RKm33lzvZ2gGnNM25GT220=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:40:20 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9xcst1d5jn"}, {"Name": "label", "Value": "uploads/profile_images/21_20250715_234020.png"}, {"Name": "integrity", "Value": "sha256-bSGwTRkG7JJxSWJtpxKk9RKm33lzvZ2gGnNM25GT220="}]}, {"Route": "uploads/profile_images/21_20250715_234020.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234020.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "29764"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"bSGwTRkG7JJxSWJtpxKk9RKm33lzvZ2gGnNM25GT220=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:40:20 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bSGwTRkG7JJxSWJtpxKk9RKm33lzvZ2gGnNM25GT220="}]}, {"Route": "uploads/profile_images/21_20250715_234816.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234816.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9693"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:48:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g="}]}, {"Route": "uploads/profile_images/21_20250715_234816.zxkacwu4rx.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_234816.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9693"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:48:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zxkacwu4rx"}, {"Name": "label", "Value": "uploads/profile_images/21_20250715_234816.png"}, {"Name": "integrity", "Value": "sha256-h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g="}]}, {"Route": "uploads/profile_images/21_20250715_235443.fago7df5v7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_235443.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25896"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:54:43 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fago7df5v7"}, {"Name": "label", "Value": "uploads/profile_images/21_20250715_235443.png"}, {"Name": "integrity", "Value": "sha256-iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg="}]}, {"Route": "uploads/profile_images/21_20250715_235443.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250715_235443.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25896"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 15 Jul 2025 23:54:43 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-iaw8FXWNJPuAqaH1bPXublGY3L3qtJVv2nYb5wWvHXg="}]}, {"Route": "uploads/profile_images/21_20250716_011418.ioah74vzm7.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250716_011418.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54472"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UvcxzcwgzCx8g+I327YOPvr20fUIBUNpuVz4nhBLYfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:14:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ioah74vzm7"}, {"Name": "label", "Value": "uploads/profile_images/21_20250716_011418.png"}, {"Name": "integrity", "Value": "sha256-UvcxzcwgzCx8g+I327YOPvr20fUIBUNpuVz4nhBLYfU="}]}, {"Route": "uploads/profile_images/21_20250716_011418.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250716_011418.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54472"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"UvcxzcwgzCx8g+I327YOPvr20fUIBUNpuVz4nhBLYfU=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:14:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UvcxzcwgzCx8g+I327YOPvr20fUIBUNpuVz4nhBLYfU="}]}, {"Route": "uploads/profile_images/21_20250723_161244.4cnl9qiz9v.jpg", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250723_161244.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20451"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZJrx2dySBKYK4ZIGHGmiAevZtOSzIhl/q3Nh6hF60kE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 16:12:44 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4cnl9qiz9v"}, {"Name": "label", "Value": "uploads/profile_images/21_20250723_161244.jpg"}, {"Name": "integrity", "Value": "sha256-ZJrx2dySBKYK4ZIGHGmiAevZtOSzIhl/q3Nh6hF60kE="}]}, {"Route": "uploads/profile_images/21_20250723_161244.jpg", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\21_20250723_161244.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20451"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZJrx2dySBKYK4ZIGHGmiAevZtOSzIhl/q3Nh6hF60kE=\""}, {"Name": "Last-Modified", "Value": "Wed, 23 Jul 2025 16:12:44 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZJrx2dySBKYK4ZIGHGmiAevZtOSzIhl/q3Nh6hF60kE="}]}, {"Route": "uploads/profile_images/22_20250716_021402.682niy8knh.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\22_20250716_021402.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9849"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"+vqXtzH1M17PVpGf9urXk3WkxZwARmvwIl0eXTwif68=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:14:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "682niy8knh"}, {"Name": "label", "Value": "uploads/profile_images/22_20250716_021402.png"}, {"Name": "integrity", "Value": "sha256-+vqXtzH1M17PVpGf9urXk3WkxZwARmvwIl0eXTwif68="}]}, {"Route": "uploads/profile_images/22_20250716_021402.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\22_20250716_021402.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9849"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"+vqXtzH1M17PVpGf9urXk3WkxZwARmvwIl0eXTwif68=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:14:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+vqXtzH1M17PVpGf9urXk3WkxZwARmvwIl0eXTwif68="}]}, {"Route": "uploads/profile_images/23_20250716_021342.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\23_20250716_021342.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11910"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"O/hKeg4sEpZq5/3WqwlklbVMeWck9RGLYISA5BFbFp0=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:13:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-O/hKeg4sEpZq5/3WqwlklbVMeWck9RGLYISA5BFbFp0="}]}, {"Route": "uploads/profile_images/23_20250716_021342.vv893ww0lz.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\23_20250716_021342.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11910"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"O/hKeg4sEpZq5/3WqwlklbVMeWck9RGLYISA5BFbFp0=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:13:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vv893ww0lz"}, {"Name": "label", "Value": "uploads/profile_images/23_20250716_021342.png"}, {"Name": "integrity", "Value": "sha256-O/hKeg4sEpZq5/3WqwlklbVMeWck9RGLYISA5BFbFp0="}]}, {"Route": "uploads/profile_images/24_20250716_011509.8zdxq49pmb.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_011509.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13303"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"/IAlo7QO6ZGzDM12/CZtlMX9fUPMCx7rzRTkjAcnuyE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:15:09 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8zdxq49pmb"}, {"Name": "label", "Value": "uploads/profile_images/24_20250716_011509.png"}, {"Name": "integrity", "Value": "sha256-/IAlo7QO6ZGzDM12/CZtlMX9fUPMCx7rzRTkjAcnuyE="}]}, {"Route": "uploads/profile_images/24_20250716_011509.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_011509.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13303"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"/IAlo7QO6ZGzDM12/CZtlMX9fUPMCx7rzRTkjAcnuyE=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:15:09 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/IAlo7QO6ZGzDM12/CZtlMX9fUPMCx7rzRTkjAcnuyE="}]}, {"Route": "uploads/profile_images/24_20250716_021311.l3bd4ywp3d.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_021311.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18938"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cQVnTrpbjdZc9IektUY9shkviCjS3BCAj9YtwMGsXyI=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:13:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l3bd4ywp3d"}, {"Name": "label", "Value": "uploads/profile_images/24_20250716_021311.png"}, {"Name": "integrity", "Value": "sha256-cQVnTrpbjdZc9IektUY9shkviCjS3BCAj9YtwMGsXyI="}]}, {"Route": "uploads/profile_images/24_20250716_021311.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\24_20250716_021311.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18938"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"cQVnTrpbjdZc9IektUY9shkviCjS3BCAj9YtwMGsXyI=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 02:13:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cQVnTrpbjdZc9IektUY9shkviCjS3BCAj9YtwMGsXyI="}]}, {"Route": "uploads/profile_images/29_20250716_011138.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\29_20250716_011138.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9693"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:11:38 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g="}]}, {"Route": "uploads/profile_images/29_20250716_011138.zxkacwu4rx.png", "AssetFile": "E:\\flutter_application_2_convertToSQL - Copy2\\webApi\\webApi\\wwwroot\\uploads\\profile_images\\29_20250716_011138.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9693"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g=\""}, {"Name": "Last-Modified", "Value": "Wed, 16 Jul 2025 01:11:38 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zxkacwu4rx"}, {"Name": "label", "Value": "uploads/profile_images/29_20250716_011138.png"}, {"Name": "integrity", "Value": "sha256-h3RLH0e3DKdeeiERRzZY+kvCUfpVms/AxCZA5JIRH/g="}]}]}