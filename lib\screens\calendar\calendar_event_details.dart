import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import '../../constants/app_colors.dart';
import '../../constants/app_styles.dart';
import '../../models/calendar_models.dart';
import '../../controllers/calendar_events_controller.dart';
import '../../controllers/task_controller.dart';
import '../../models/task_model.dart';
import '../../services/unified_permission_service.dart';
import 'calendar_event_form.dart';

/// شاشة تفاصيل الحدث المتقدمة
class CalendarEventDetails extends StatefulWidget {
  final CalendarEvent event;
  final VoidCallback? onEventUpdated;
  final VoidCallback? onEventDeleted;

  const CalendarEventDetails({
    super.key,
    required this.event,
    this.onEventUpdated,
    this.onEventDeleted,
  });

  @override
  State<CalendarEventDetails> createState() => _CalendarEventDetailsState();
}

class _CalendarEventDetailsState extends State<CalendarEventDetails> {
  final CalendarEventsController _calendarController = Get.find<CalendarEventsController>();
  final TaskController _taskController = Get.find<TaskController>();
  final UnifiedPermissionService _permissionService = Get.find<UnifiedPermissionService>();
  
  Task? _relatedTask;
  bool _isLoading = false;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadRelatedTask();
  }

  /// تحميل المهمة المرتبطة بالحدث
  Future<void> _loadRelatedTask() async {
    if (widget.event.taskId != null) {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      try {
        await _taskController.loadTaskDetails(widget.event.taskId!.toString());
        final task = _taskController.currentTask;

        setState(() {
          _relatedTask = task;
          _isLoading = false;
        });
      } catch (e) {
        setState(() {
          _errorMessage = 'حدث خطأ أثناء تحميل المهمة المرتبطة: $e';
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('تفاصيل الحدث'),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: AppColors.white,
        elevation: 0,
        actions: [
          if (_permissionService.canEditCalendarEvents())
            IconButton(
              icon: const Icon(Icons.edit),
              tooltip: 'تعديل الحدث',
              onPressed: _editEvent,
            ),
          if (_permissionService.canDeleteCalendarEvents())
            IconButton(
              icon: const Icon(Icons.delete),
              tooltip: 'حذف الحدث',
              onPressed: _confirmDeleteEvent,
            ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            tooltip: 'المزيد من الخيارات',
            onSelected: _handleMenuAction,
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'share',
                child: Row(
                  children: [
                    Icon(Icons.share),
                    SizedBox(width: 8),
                    Text('مشاركة الحدث'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'duplicate',
                child: Row(
                  children: [
                    Icon(Icons.copy),
                    SizedBox(width: 8),
                    Text('نسخ الحدث'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'export',
                child: Row(
                  children: [
                    Icon(Icons.download),
                    SizedBox(width: 8),
                    Text('تصدير الحدث'),
                  ],
                ),
              ),
              if (widget.event.taskId != null)
                const PopupMenuItem(
                  value: 'task_details',
                  child: Row(
                    children: [
                      Icon(Icons.task),
                      SizedBox(width: 8),
                      Text('تفاصيل المهمة'),
                    ],
                  ),
                ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildEventDetails(),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// بناء محتوى تفاصيل الحدث
  Widget _buildEventDetails() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildEventHeader(),
          const SizedBox(height: 20),
          _buildEventDetailsCard(),
          const SizedBox(height: 20),
          if (widget.event.taskId != null) ...[
            _buildRelatedTaskCard(),
            const SizedBox(height: 20),
          ],
          _buildQuickActions(),
        ],
      ),
    );
  }

  /// بناء رأس الحدث
  Widget _buildEventHeader() {
    final eventColor = _parseColor(widget.event.color) ?? _getEventTypeColor(widget.event.eventType);
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            eventColor.withAlpha(200),
            eventColor.withAlpha(100),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: eventColor.withAlpha(100),
            blurRadius: 10,
            spreadRadius: 2,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.white.withAlpha(200),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.getShadowColor(0.2),
                      blurRadius: 4,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Icon(
                  _getEventTypeIcon(widget.event.eventType),
                  color: eventColor,
                  size: 28,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.event.title,
                      style: AppStyles.headingLarge.copyWith(
                        color: AppColors.white,
                        fontWeight: FontWeight.bold,
                        shadows: [
                          Shadow(
                            color: AppColors.getShadowColor(0.3),
                            blurRadius: 2,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 6),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                      decoration: BoxDecoration(
                        color: AppColors.white.withAlpha(150),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        _getEventTypeName(widget.event.eventType),
                        style: AppStyles.bodySmall.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          
          // وصف الحدث إذا كان موجوداً
          if (widget.event.description?.isNotEmpty == true) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.white.withAlpha(150),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                widget.event.description!,
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء بطاقة تفاصيل الحدث
  Widget _buildEventDetailsCard() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'تفاصيل الحدث',
              style: AppStyles.headingMedium.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              Icons.access_time,
              'التوقيت',
              _formatEventTime(),
            ),
            _buildDetailRow(
              Icons.schedule,
              'المدة',
              _formatEventDuration(),
            ),
            if (widget.event.location?.isNotEmpty == true)
              _buildDetailRow(
                Icons.location_on,
                'الموقع',
                widget.event.location!,
              ),
            if (widget.event.isReminderEnabled)
              _buildDetailRow(
                Icons.notifications,
                'التذكير',
                '${widget.event.reminderMinutes} دقيقة قبل الحدث',
              ),
            _buildDetailRow(
              Icons.person,
              'المنشئ',
              'المستخدم #${widget.event.userId}',
            ),
            _buildDetailRow(
              Icons.calendar_today,
              'تاريخ الإنشاء',
              _formatTimestamp(widget.event.createdAt),
            ),
            if (widget.event.updatedAt != null)
              _buildDetailRow(
                Icons.update,
                'آخر تحديث',
                _formatTimestamp(widget.event.updatedAt!),
              ),
          ],
        ),
      ),
    );
  }

  /// بناء صف تفاصيل
  Widget _buildDetailRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            size: 20,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: AppStyles.bodySmall.copyWith(
                    color: AppColors.textSecondary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: AppStyles.bodyMedium.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// معالجة إجراءات القائمة
  void _handleMenuAction(String action) {
    switch (action) {
      case 'share':
        _shareEvent();
        break;
      case 'duplicate':
        _duplicateEvent();
        break;
      case 'export':
        _exportEvent();
        break;
      case 'task_details':
        _showTaskDetails();
        break;
    }
  }

  /// تعديل الحدث
  void _editEvent() {
    Get.to(() => CalendarEventForm(
      initialDate: DateTime.fromMillisecondsSinceEpoch(widget.event.startTime * 1000),
      // eventToEdit: widget.event, // سيتم إضافة هذه الميزة لاحقاً
      onEventCreated: (updatedEvent) {
        widget.onEventUpdated?.call();
        Get.back();
      },
    ));
  }

  /// حذف الحدث
  void _confirmDeleteEvent() {
    Get.dialog(
      AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف الحدث "${widget.event.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Get.back(),
            child: const Text('إلغاء'),
          ),
          ElevatedButton(
            onPressed: _deleteEvent,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: AppColors.white,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );
  }

  /// حذف الحدث
  void _deleteEvent() async {
    try {
      await _calendarController.deleteEvent(widget.event.id);
      widget.onEventDeleted?.call();
      Get.back(); // إغلاق الحوار
      Get.back(); // العودة للشاشة السابقة

      Get.snackbar(
        'تم الحذف',
        'تم حذف الحدث بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.success,
        colorText: AppColors.white,
      );
    } catch (e) {
      Get.back(); // إغلاق الحوار
      Get.snackbar(
        'خطأ',
        'فشل في حذف الحدث: $e',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: AppColors.error,
        colorText: AppColors.white,
      );
    }
  }

  /// مشاركة الحدث
  void _shareEvent() {
    final eventText = '''
📅 ${widget.event.title}

📝 الوصف: ${widget.event.description ?? 'لا يوجد وصف'}

🕐 التوقيت: ${_formatEventTime()}

${widget.event.location?.isNotEmpty == true ? '📍 الموقع: ${widget.event.location}' : ''}

تم إنشاؤه في: ${DateFormat('dd/MM/yyyy HH:mm', 'ar').format(DateTime.fromMillisecondsSinceEpoch(widget.event.createdAt * 1000))}
    ''';

    Get.snackbar(
      'مشاركة الحدث',
      'تم نسخ تفاصيل الحدث للمشاركة',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.success,
      colorText: AppColors.white,
      icon: const Icon(Icons.share, color: AppColors.white),
    );

    debugPrint('مشاركة الحدث: $eventText');
  }

  /// نسخ الحدث
  void _duplicateEvent() {
    Get.to(() => CalendarEventForm(
      initialDate: DateTime.fromMillisecondsSinceEpoch(widget.event.startTime * 1000),
      onEventCreated: (newEvent) {
        Get.snackbar(
          'تم النسخ بنجاح',
          'تم إنشاء نسخة من الحدث',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: AppColors.success,
          colorText: AppColors.white,
          icon: const Icon(Icons.copy, color: AppColors.white),
        );
        widget.onEventUpdated?.call();
      },
    ));
  }

  /// تصدير الحدث
  void _exportEvent() {
    Get.snackbar(
      'تصدير الحدث',
      'سيتم إضافة ميزة التصدير قريباً',
      snackPosition: SnackPosition.BOTTOM,
      backgroundColor: AppColors.info,
      colorText: AppColors.white,
      icon: const Icon(Icons.download, color: AppColors.white),
    );
  }

  /// عرض تفاصيل المهمة
  void _showTaskDetails() {
    if (widget.event.taskId != null) {
      Get.toNamed('/task-details', arguments: {'taskId': widget.event.taskId});
    }
  }

  /// تنسيق وقت الحدث
  String _formatEventTime() {
    final startTime = DateTime.fromMillisecondsSinceEpoch(widget.event.startTime * 1000);
    final endTime = DateTime.fromMillisecondsSinceEpoch(widget.event.endTime * 1000);

    if (widget.event.allDay) {
      return 'طوال اليوم - ${DateFormat('dd/MM/yyyy', 'ar').format(startTime)}';
    }

    final dateFormat = DateFormat('dd/MM/yyyy', 'ar');
    final timeFormat = DateFormat('HH:mm', 'ar');

    if (startTime.day == endTime.day && startTime.month == endTime.month && startTime.year == endTime.year) {
      return '${dateFormat.format(startTime)} من ${timeFormat.format(startTime)} إلى ${timeFormat.format(endTime)}';
    } else {
      return 'من ${dateFormat.format(startTime)} ${timeFormat.format(startTime)} إلى ${dateFormat.format(endTime)} ${timeFormat.format(endTime)}';
    }
  }

  /// تنسيق مدة الحدث
  String _formatEventDuration() {
    final duration = Duration(seconds: widget.event.endTime - widget.event.startTime);

    if (widget.event.allDay) {
      final days = duration.inDays;
      return days == 1 ? 'يوم واحد' : '$days أيام';
    }

    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;

    if (hours == 0) {
      return '$minutes دقيقة';
    } else if (minutes == 0) {
      return '$hours ساعة';
    } else {
      return '$hours ساعة و $minutes دقيقة';
    }
  }

  /// تنسيق الطابع الزمني
  String _formatTimestamp(int timestamp) {
    final dateTime = DateTime.fromMillisecondsSinceEpoch(timestamp * 1000);
    return DateFormat('dd/MM/yyyy HH:mm', 'ar').format(dateTime);
  }

  /// تحليل اللون من النص
  Color? _parseColor(String? colorString) {
    if (colorString == null || colorString.isEmpty) return null;
    try {
      return Color(int.parse(colorString.replaceFirst('#', '0xFF')));
    } catch (e) {
      return null;
    }
  }

  /// الحصول على لون نوع الحدث
  Color _getEventTypeColor(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return AppColors.primary;
      case CalendarEventType.meeting:
        return AppColors.info;
      case CalendarEventType.reminder:
        return AppColors.warning;
      case CalendarEventType.vacation:
        return AppColors.success;
      case CalendarEventType.other:
        return AppColors.info;
      default:
        return AppColors.primary;
    }
  }

  /// الحصول على أيقونة نوع الحدث
  IconData _getEventTypeIcon(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return Icons.task;
      case CalendarEventType.meeting:
        return Icons.meeting_room;
      case CalendarEventType.reminder:
        return Icons.notifications;
      case CalendarEventType.vacation:
        return Icons.beach_access;
      case CalendarEventType.other:
        return Icons.event;
      default:
        return Icons.event;
    }
  }

  /// الحصول على اسم نوع الحدث
  String _getEventTypeName(CalendarEventType eventType) {
    switch (eventType) {
      case CalendarEventType.task:
        return 'مهمة';
      case CalendarEventType.meeting:
        return 'اجتماع';
      case CalendarEventType.reminder:
        return 'تذكير';
      case CalendarEventType.vacation:
        return 'إجازة';
      case CalendarEventType.other:
        return 'حدث آخر';
      default:
        return 'حدث';
    }
  }

  /// الحصول على لون حالة المهمة
  Color _getTaskStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return AppColors.warning;
      case 'in_progress':
        return AppColors.info;
      case 'completed':
        return AppColors.success;
      case 'cancelled':
        return AppColors.error;
      default:
        return AppColors.textSecondary;
    }
  }

  /// الحصول على نص حالة المهمة
  String _getTaskStatusText(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'معلقة';
      case 'in_progress':
        return 'جارية';
      case 'completed':
        return 'مكتملة';
      case 'cancelled':
        return 'ملغاة';
      default:
        return status;
    }
  }

  /// الحصول على لون أولوية المهمة
  Color _getTaskPriorityColor(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return AppColors.error;
      case 'medium':
        return AppColors.warning;
      case 'low':
        return AppColors.success;
      default:
        return AppColors.textSecondary;
    }
  }

  /// الحصول على نص أولوية المهمة
  String _getTaskPriorityText(String priority) {
    switch (priority.toLowerCase()) {
      case 'high':
        return 'عالية';
      case 'medium':
        return 'متوسطة';
      case 'low':
        return 'منخفضة';
      default:
        return priority;
    }
  }

  /// بناء بطاقة المهمة المرتبطة
  Widget _buildRelatedTaskCard() {
    if (_relatedTask == null) {
      return Card(
        elevation: 4,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              Icon(
                Icons.task_alt,
                size: 48,
                color: AppColors.textSecondary,
              ),
              const SizedBox(height: 8),
              Text(
                'لا توجد مهمة مرتبطة',
                style: AppStyles.bodyMedium.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(12),
        onTap: () => _showTaskDetails(),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.task_alt,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'المهمة المرتبطة',
                    style: AppStyles.headingMedium.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: 16,
                    color: AppColors.textSecondary,
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                _relatedTask!.title,
                style: AppStyles.bodyLarge.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 8),
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTaskStatusColor(_relatedTask!.status).withAlpha(100),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getTaskStatusText(_relatedTask!.status),
                      style: AppStyles.bodySmall.copyWith(
                        color: _getTaskStatusColor(_relatedTask!.status),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTaskPriorityColor(_relatedTask!.priority).withAlpha(100),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getTaskPriorityText(_relatedTask!.priority),
                      style: AppStyles.bodySmall.copyWith(
                        color: _getTaskPriorityColor(_relatedTask!.priority),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// بناء الإجراءات السريعة
  Widget _buildQuickActions() {
    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إجراءات سريعة',
              style: AppStyles.headingMedium.copyWith(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildActionButton(
                    Icons.edit,
                    'تعديل',
                    AppColors.primary,
                    _editEvent,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildActionButton(
                    Icons.copy,
                    'نسخ',
                    AppColors.info,
                    _duplicateEvent,
                  ),
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: _buildActionButton(
                    Icons.share,
                    'مشاركة',
                    AppColors.success,
                    _shareEvent,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// بناء زر إجراء
  Widget _buildActionButton(IconData icon, String label, Color color, VoidCallback onPressed) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.withAlpha(100),
        foregroundColor: color,
        elevation: 0,
        padding: const EdgeInsets.symmetric(vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 20),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppStyles.bodySmall.copyWith(
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// بناء زر الإجراء العائم
  Widget? _buildFloatingActionButton() {
    if (!_permissionService.canEditCalendarEvents()) {
      return null;
    }

    return FloatingActionButton.extended(
      onPressed: _editEvent,
      icon: const Icon(Icons.edit),
      label: const Text('تعديل'),
      backgroundColor: Theme.of(context).primaryColor,
      foregroundColor: AppColors.white,
    );
  }
}
