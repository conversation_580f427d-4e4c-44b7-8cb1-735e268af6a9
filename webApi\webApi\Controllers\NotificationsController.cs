using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using webApi.Models;
using webApi.Services;

namespace webApi.Controllers
{
    /// <summary>
    /// وحدة تحكم لإدارة الإشعارات في النظام
    /// </summary>
    [Route("api/[controller]")]
    [ApiController]
    [Produces("application/json")]
    public class NotificationsController : ControllerBase
    {
        private readonly TasksDbContext _context;
        private readonly ILoggingService _loggingService;
        private readonly INotificationService _notificationService;

        public NotificationsController(TasksDbContext context, ILoggingService loggingService, INotificationService notificationService)
        {
            _context = context;
            _loggingService = loggingService;
            _notificationService = notificationService;
        }

        /// <summary>
        /// الحصول على جميع الإشعارات
        /// </summary>
        /// <returns>قائمة بجميع الإشعارات</returns>
        /// <response code="200">إرجاع قائمة الإشعارات</response>
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Notification>>> GetNotifications()
        {
            return await _context.Notifications
                .Include(n => n.User)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على إشعار محدد
        /// </summary>
        /// <param name="id">معرف الإشعار</param>
        /// <returns>الإشعار المطلوب</returns>
        /// <response code="200">إرجاع الإشعار</response>
        /// <response code="404">الإشعار غير موجود</response>
        [HttpGet("{id}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<ActionResult<Notification>> GetNotification(int id)
        {
            var notification = await _context.Notifications
                .Include(n => n.User)
                .FirstOrDefaultAsync(n => n.Id == id);

            if (notification == null)
            {
                return NotFound();
            }

            return notification;
        }

        /// <summary>
        /// الحصول على إشعارات مستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة إشعارات المستخدم</returns>
        /// <response code="200">إرجاع قائمة الإشعارات</response>
        [HttpGet("user/{userId}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Notification>>> GetUserNotifications(int userId)
        {
            return await _context.Notifications
                .Where(n => n.UserId == userId)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// الحصول على الإشعارات غير المقروءة لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة الإشعارات غير المقروءة</returns>
        /// <response code="200">إرجاع قائمة الإشعارات غير المقروءة</response>
        [HttpGet("user/{userId}/unread")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<IEnumerable<Notification>>> GetUnreadNotifications(int userId)
        {
            return await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .OrderByDescending(n => n.CreatedAt)
                .ToListAsync();
        }

        /// <summary>
        /// عدد الإشعارات غير المقروءة لمستخدم محدد
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>عدد الإشعارات غير المقروءة</returns>
        /// <response code="200">إرجاع العدد</response>
        [HttpGet("user/{userId}/unread/count")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult<int>> GetUnreadNotificationsCount(int userId)
        {
            var count = await _context.Notifications
                .CountAsync(n => n.UserId == userId && !n.IsRead);

            return count;
        }

        /// <summary>
        /// إنشاء إشعار جديد
        /// </summary>
        /// <param name="notification">بيانات الإشعار</param>
        /// <returns>الإشعار المُنشأ</returns>
        /// <response code="201">تم إنشاء الإشعار بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        [HttpPost]
        [ProducesResponseType(StatusCodes.Status201Created)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        public async Task<ActionResult<Notification>> PostNotification(Notification notification)
        {
            try
            {
                // استخدام NotificationService لإنشاء وإرسال الإشعار
                var createdNotification = await _notificationService.CreateAndSendNotificationAsync(
                    notification.UserId,
                    notification.Title,
                    notification.Content,
                    notification.Type,
                    notification.RelatedId
                );

                // تسجيل إنشاء الإشعار
                var currentUserId = GetCurrentUserId();
                if (currentUserId > 0)
                {
                    await _loggingService.LogActivityAsync(
                        "create_notification",
                        "notification",
                        createdNotification.Id,
                        currentUserId,
                        $"إنشاء إشعار للمستخدم #{createdNotification.UserId}: {createdNotification.Title}");
                }

                return CreatedAtAction("GetNotification", new { id = createdNotification.Id }, createdNotification);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, "خطأ في إنشاء الإشعار");
            }
        }

        /// <summary>
        /// تحديث إشعار
        /// </summary>
        /// <param name="id">معرف الإشعار</param>
        /// <param name="notification">بيانات الإشعار المحدثة</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث الإشعار بنجاح</response>
        /// <response code="400">طلب غير صالح</response>
        /// <response code="404">الإشعار غير موجود</response>
        [HttpPut("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status400BadRequest)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> PutNotification(int id, Notification notification)
        {
            if (id != notification.Id)
            {
                return BadRequest();
            }

            _context.Entry(notification).State = EntityState.Modified;

            try
            {
                await _context.SaveChangesAsync();
            }
            catch (DbUpdateConcurrencyException)
            {
                if (!NotificationExists(id))
                {
                    return NotFound();
                }
                else
                {
                    throw;
                }
            }

            // تسجيل تحديث الإشعار
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "update_notification",
                    "notification",
                    id,
                    currentUserId,
                    $"تحديث إشعار: {notification.Title}");
            }

            return NoContent();
        }

        /// <summary>
        /// تحديد إشعار كمقروء
        /// </summary>
        /// <param name="id">معرف الإشعار</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث الإشعار بنجاح</response>
        /// <response code="404">الإشعار غير موجود</response>
        [HttpPatch("{id}/mark-read")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> MarkAsRead(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
            {
                return NotFound();
            }

            notification.IsRead = true;
            await _context.SaveChangesAsync();

            // تسجيل قراءة الإشعار
            var currentUserId = GetCurrentUserId();
            if (currentUserId > 0)
            {
                await _loggingService.LogActivityAsync(
                    "read_notification",
                    "notification",
                    id,
                    currentUserId,
                    $"قراءة إشعار: {notification.Title}");
            }

            return NoContent();
        }

        /// <summary>
        /// تحديد جميع إشعارات المستخدم كمقروءة
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم تحديث الإشعارات بنجاح</response>
        [HttpPatch("user/{userId}/mark-all-read")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        public async Task<IActionResult> MarkAllAsRead(int userId)
        {
            var notifications = await _context.Notifications
                .Where(n => n.UserId == userId && !n.IsRead)
                .ToListAsync();

            foreach (var notification in notifications)
            {
                notification.IsRead = true;
            }

            await _context.SaveChangesAsync();
            return NoContent();
        }

        /// <summary>
        /// حذف إشعار
        /// </summary>
        /// <param name="id">معرف الإشعار</param>
        /// <returns>لا يوجد محتوى</returns>
        /// <response code="204">تم حذف الإشعار بنجاح</response>
        /// <response code="404">الإشعار غير موجود</response>
        [HttpDelete("{id}")]
        [ProducesResponseType(StatusCodes.Status204NoContent)]
        [ProducesResponseType(StatusCodes.Status404NotFound)]
        public async Task<IActionResult> DeleteNotification(int id)
        {
            var notification = await _context.Notifications.FindAsync(id);
            if (notification == null)
            {
                return NotFound();
            }

            _context.Notifications.Remove(notification);
            await _context.SaveChangesAsync();

            return NoContent();
        }

        private bool NotificationExists(int id)
        {
            return _context.Notifications.Any(e => e.Id == id);
        }

        /// <summary>
        /// الحصول على معرف المستخدم الحالي من JWT token
        /// </summary>
        private int GetCurrentUserId()
        {
            try
            {
                var userIdClaim = User.FindFirst("UserId")?.Value ??
                                 User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value;

                if (int.TryParse(userIdClaim, out int userId))
                {
                    return userId;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }
    }
}
