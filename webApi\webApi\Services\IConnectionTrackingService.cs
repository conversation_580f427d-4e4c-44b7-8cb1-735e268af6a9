using System.Collections.Concurrent;

namespace webApi.Services
{
    /// <summary>
    /// واجهة خدمة تتبع الاتصالات النشطة
    /// </summary>
    public interface IConnectionTrackingService
    {
        /// <summary>
        /// إضافة اتصال جديد للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <param name="connectionId">معرف الاتصال</param>
        /// <param name="deviceInfo">معلومات الجهاز (اختياري)</param>
        Task AddConnectionAsync(int userId, string connectionId, string? deviceInfo = null);

        /// <summary>
        /// إزالة اتصال للمستخدم
        /// </summary>
        /// <param name="connectionId">معرف الاتصال</param>
        Task RemoveConnectionAsync(string connectionId);

        /// <summary>
        /// الحصول على جميع الاتصالات النشطة للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>قائمة معرفات الاتصالات النشطة</returns>
        Task<List<string>> GetUserConnectionsAsync(int userId);

        /// <summary>
        /// التحقق من وجود اتصالات نشطة للمستخدم
        /// </summary>
        /// <param name="userId">معرف المستخدم</param>
        /// <returns>true إذا كان المستخدم متصل</returns>
        Task<bool> IsUserOnlineAsync(int userId);

        /// <summary>
        /// الحصول على معرف المستخدم من معرف الاتصال
        /// </summary>
        /// <param name="connectionId">معرف الاتصال</param>
        /// <returns>معرف المستخدم أو null</returns>
        Task<int?> GetUserIdByConnectionAsync(string connectionId);

        /// <summary>
        /// تنظيف الاتصالات المنقطعة
        /// </summary>
        Task CleanupDisconnectedConnectionsAsync();
    }

    /// <summary>
    /// معلومات الاتصال
    /// </summary>
    public class ConnectionInfo
    {
        public string ConnectionId { get; set; } = string.Empty;
        public int UserId { get; set; }
        public DateTime ConnectedAt { get; set; }
        public string? DeviceInfo { get; set; }
        public DateTime LastActivity { get; set; }
    }
}
