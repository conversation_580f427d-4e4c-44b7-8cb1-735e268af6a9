using System.Collections.Concurrent;

namespace webApi.Services
{
    /// <summary>
    /// خدمة تتبع الاتصالات النشطة في الذاكرة
    /// </summary>
    public class ConnectionTrackingService : IConnectionTrackingService
    {
        private readonly ConcurrentDictionary<string, ConnectionInfo> _connections = new();
        private readonly ConcurrentDictionary<int, HashSet<string>> _userConnections = new();
        private readonly ILogger<ConnectionTrackingService> _logger;
        private readonly object _lock = new object();

        public ConnectionTrackingService(ILogger<ConnectionTrackingService> logger)
        {
            _logger = logger;
        }

        /// <summary>
        /// إضافة اتصال جديد للمستخدم
        /// </summary>
        public Task AddConnectionAsync(int userId, string connectionId, string? deviceInfo = null)
        {
            try
            {
                var connectionInfo = new ConnectionInfo
                {
                    ConnectionId = connectionId,
                    UserId = userId,
                    ConnectedAt = DateTime.UtcNow,
                    DeviceInfo = deviceInfo,
                    LastActivity = DateTime.UtcNow
                };

                _connections.TryAdd(connectionId, connectionInfo);

                lock (_lock)
                {
                    if (!_userConnections.ContainsKey(userId))
                    {
                        _userConnections[userId] = new HashSet<string>();
                    }
                    _userConnections[userId].Add(connectionId);
                }

                _logger.LogInformation("تم إضافة اتصال جديد: المستخدم {UserId}, الاتصال {ConnectionId}, الجهاز {DeviceInfo}",
                    userId, connectionId, deviceInfo ?? "غير محدد");

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إضافة اتصال: المستخدم {UserId}, الاتصال {ConnectionId}", userId, connectionId);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// إزالة اتصال للمستخدم
        /// </summary>
        public Task RemoveConnectionAsync(string connectionId)
        {
            try
            {
                if (_connections.TryRemove(connectionId, out var connectionInfo))
                {
                    lock (_lock)
                    {
                        if (_userConnections.ContainsKey(connectionInfo.UserId))
                        {
                            _userConnections[connectionInfo.UserId].Remove(connectionId);
                            
                            // إزالة المستخدم من القاموس إذا لم تعد له اتصالات
                            if (_userConnections[connectionInfo.UserId].Count == 0)
                            {
                                _userConnections.TryRemove(connectionInfo.UserId, out _);
                            }
                        }
                    }

                    _logger.LogInformation("تم إزالة اتصال: المستخدم {UserId}, الاتصال {ConnectionId}",
                        connectionInfo.UserId, connectionId);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في إزالة اتصال: {ConnectionId}", connectionId);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// الحصول على جميع الاتصالات النشطة للمستخدم
        /// </summary>
        public Task<List<string>> GetUserConnectionsAsync(int userId)
        {
            try
            {
                lock (_lock)
                {
                    if (_userConnections.ContainsKey(userId))
                    {
                        return Task.FromResult(_userConnections[userId].ToList());
                    }
                }

                return Task.FromResult(new List<string>());
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على اتصالات المستخدم: {UserId}", userId);
                return Task.FromResult(new List<string>());
            }
        }

        /// <summary>
        /// التحقق من وجود اتصالات نشطة للمستخدم
        /// </summary>
        public Task<bool> IsUserOnlineAsync(int userId)
        {
            try
            {
                lock (_lock)
                {
                    return Task.FromResult(_userConnections.ContainsKey(userId) && _userConnections[userId].Count > 0);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في فحص حالة المستخدم: {UserId}", userId);
                return Task.FromResult(false);
            }
        }

        /// <summary>
        /// الحصول على معرف المستخدم من معرف الاتصال
        /// </summary>
        public Task<int?> GetUserIdByConnectionAsync(string connectionId)
        {
            try
            {
                if (_connections.TryGetValue(connectionId, out var connectionInfo))
                {
                    return Task.FromResult<int?>(connectionInfo.UserId);
                }

                return Task.FromResult<int?>(null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على معرف المستخدم: {ConnectionId}", connectionId);
                return Task.FromResult<int?>(null);
            }
        }

        /// <summary>
        /// تنظيف الاتصالات المنقطعة
        /// </summary>
        public Task CleanupDisconnectedConnectionsAsync()
        {
            try
            {
                var cutoffTime = DateTime.UtcNow.AddMinutes(-30); // اتصالات أقدم من 30 دقيقة
                var disconnectedConnections = _connections
                    .Where(kvp => kvp.Value.LastActivity < cutoffTime)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var connectionId in disconnectedConnections)
                {
                    RemoveConnectionAsync(connectionId);
                }

                if (disconnectedConnections.Count > 0)
                {
                    _logger.LogInformation("تم تنظيف {Count} اتصال منقطع", disconnectedConnections.Count);
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تنظيف الاتصالات المنقطعة");
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// تحديث آخر نشاط للاتصال
        /// </summary>
        public Task UpdateLastActivityAsync(string connectionId)
        {
            try
            {
                if (_connections.TryGetValue(connectionId, out var connectionInfo))
                {
                    connectionInfo.LastActivity = DateTime.UtcNow;
                }

                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في تحديث آخر نشاط: {ConnectionId}", connectionId);
                return Task.CompletedTask;
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الاتصالات
        /// </summary>
        public Task<object> GetConnectionStatsAsync()
        {
            try
            {
                lock (_lock)
                {
                    var stats = new
                    {
                        TotalConnections = _connections.Count,
                        OnlineUsers = _userConnections.Count,
                        AverageConnectionsPerUser = _userConnections.Count > 0 
                            ? (double)_connections.Count / _userConnections.Count 
                            : 0
                    };

                    return Task.FromResult<object>(stats);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "خطأ في الحصول على إحصائيات الاتصالات");
                return Task.FromResult<object>(new { Error = "خطأ في الحصول على الإحصائيات" });
            }
        }
    }
}
